# Automated DAO Proposal Scraper with Activist Filter

This scraper automatically extracts proposals from **both Tally and Snapshot**, extracts comprehensive data, and applies two-stage activist filtering.

## 🚀 **Dual-Source Scraping**

### **Tally Scraping** (Automated Navigation)
1. **Opens https://www.tally.xyz/explore**
2. **Loops through ALL DAOs** (clicks `class="chakra-link css-nuz2iz"`)
3. **For each DAO**: Clicks proposals tab (`class="chakra-text css-0"`)
4. **Loops through ALL proposals** (clicks `class="chakra-text css-r5m49"`)
5. **Extracts all data** directly from each proposal page
6. **Navigates back** automatically

### **Snapshot Scraping** (GraphQL API)
1. **Fetches all Snapshot spaces** via GraphQL
2. **Gets proposals for each space**
3. **Fetches voting data** for each proposal
4. **Calculates metrics** (voting power, top voters, etc.)
5. **Fast API-based extraction**

## Features

- **🤖 Fully Automated**: Scrapes both Tally and <PERSON>napshot automatically
- **📊 Complete Data Extraction**: All 17 data points from both sources
- **🎯 Two-Stage Filtering**: `filter_before` + `filter_after` methods
- **🔄 Smart Navigation**: Handles Tally clicking and Snapshot API calls
- **💾 Multiple Output Files**: Raw data + filtered results by source
- **🛡️ Error Handling**: Continues if individual pages/APIs fail
- **⚡ Dual Speed**: Fast Snapshot API + thorough Tally navigation

## Data Points Extracted

1. **DAO Name**
2. **Proposal Title**
3. **Proposal Content/Body**
4. **Proposer**
5. **Proposer Voting Power**
6. **Proposer Voting Power Percentage**
7. **Proposer Vote Choice**
8. **Start Time**
9. **End Time**
10. **Proposal State**
11. **Voting Choices**
12. **Total Votes**
13. **Total Voting Power**
14. **Top Voter**
15. **Top Voter Power**
16. **Top Voter Percentage**
17. **Outcome**

## Installation

```bash
pip install selenium beautifulsoup4 transformers torch requests
```

You'll also need ChromeDriver installed and in your PATH.

## 🎯 **Two-Stage Filtering Process**

### **Stage 1: filter_before**
- ✅ **Keyword matching** for activist terms
- ✅ **BART NLP classification** (threshold: 0.6)
- ✅ **Fast initial filtering**

### **Stage 2: filter_after**
- ✅ **DistilBERT classification** (threshold: 0.5)
- ✅ **Applied only when enough data** (≥10 proposals)
- ✅ **Fine-tuned activist detection**

## Usage

### 🤖 **Method 1: Automated Scraping (Recommended)**

**Scrapes ALL DAOs and proposals automatically:**

```bash
python tally_scraper_complete.py
# Choose option 1 when prompted
```

**What it does:**
1. Opens Tally explore page
2. Finds all DAO links automatically
3. Clicks through each DAO's proposals
4. Extracts all data points
5. Applies both filtering stages
6. Saves comprehensive results

### 📝 **Method 2: Manual URL Scraping**

**For specific proposals only:**

1. **Add URLs to `tally_urls.txt`**:
   ```
   https://www.tally.xyz/gov/arbitrum/proposal/[proposal_id]
   https://www.tally.xyz/gov/compound/proposal/[proposal_id]
   ```

2. **Run the scraper**:
   ```bash
   python tally_scraper_complete.py
   # Choose option 2 when prompted
   ```

## 📁 **Output Files**

### **Automated Scraping Output:**
1. **`all_tally_proposals.csv/json`** - ALL scraped proposals (raw data)
2. **`stage1_filtered_proposals.csv/json`** - After filter_before
3. **`final_activist_proposals.csv/json`** - After both filters ⭐ **MAIN RESULT**
4. **`debug_tally.html`** - Last page HTML for debugging

### **Manual Scraping Output:**
1. **`all_manual_proposals.csv/json`** - All manually scraped proposals
2. **`filtered_manual_proposals.csv/json`** - Filtered results

## Activist Filter

The filter uses the same methodology as `scraper2.py`:

1. **Keyword Matching**: Checks for activist-related keywords in proposal title and content
2. **NLP Classification**: Uses BART model for zero-shot classification with 0.6 threshold
3. **Fallback**: If NLP fails, accepts proposals based on keywords only

### Keywords Include:
- Governance changes: "replace", "remove", "elect", "amend constitution"
- Financial control: "withdraw funds", "budget change", "treasury control"
- Power shifts: "change voting power", "remove delegation"
- Project control: "pivot", "terminate project", "halt"
- Legal actions: "lawsuit", "ban", "suspend"

## HTML/CSS Selectors Used

The scraper uses multiple fallback selectors for each data point:

```python
# Example selectors for proposer information
"proposer": [
    '[data-testid*="proposer"]',
    '[data-testid*="author"]',
    '.proposer',
    '.proposal-author'
]
```

## Getting Full HTML

If you need to get the complete HTML of a Tally page for analysis:

```bash
python get_full_html.py
```

This will save the full rendered HTML to `tally_proposal_full.html`.

## Troubleshooting

### Common Issues:

1. **ChromeDriver not found**: Install ChromeDriver and add to PATH
2. **Selenium timeout**: Increase `wait_time` parameter
3. **NLP model download**: First run downloads the BART model (~1.6GB)
4. **Rate limiting**: The scraper includes 3-second delays between requests

### Debug Steps:

1. Check `debug_tally.html` to see the actual page structure
2. Verify URLs are accessible manually
3. Check console output for specific error messages

## Customization

### Adding New Selectors

If Tally changes their HTML structure, update the selector lists in each extraction function:

```python
def extract_proposer(soup):
    selectors = [
        '[data-testid*="proposer"]',  # Add new selectors here
        '.new-proposer-class',
        # ... existing selectors
    ]
```

### Modifying Filter Criteria

To change the activist filter:

1. **Add keywords**: Update the `keywords` list in `filter_before()`
2. **Change NLP threshold**: Modify the `0.6` threshold
3. **Update hypothesis**: Change the classification hypothesis template

## Example Output

```json
{
  "url": "https://www.tally.xyz/gov/arbitrum/proposal/...",
  "dao_name": "Arbitrum",
  "proposal_title": "[CONSTITUTIONAL] Remove Cost Cap, Update Executors...",
  "proposer": "0x1234...abcd",
  "proposer_voting_power": "1,234,567",
  "proposal_state": "Executed",
  "activist_score": 0.85,
  "outcome": "Passed"
}
```

## Performance

- **Speed**: ~30-45 seconds per proposal (including page load and NLP)
- **Memory**: ~2GB RAM (for NLP model)
- **Network**: Respectful 3-second delays between requests
