# Tally Proposal Scraper with Activist Filter

This scraper extracts comprehensive data from Tally governance proposals and filters them for activist content using the same methodology as `scraper2.py`.

## Features

- **Complete Data Extraction**: Scrapes all the data points you requested
- **Activist Filtering**: Uses keyword matching + NLP classification to identify activist proposals
- **Multiple Proposals**: Can scrape multiple proposals in batch
- **Full HTML Rendering**: Uses Selenium to handle JavaScript-rendered content
- **Flexible URL Input**: Load URLs from file or use defaults

## Data Points Extracted

1. **DAO Name**
2. **Proposal Title**
3. **Proposal Content/Body**
4. **Proposer**
5. **Proposer Voting Power**
6. **Proposer Voting Power Percentage**
7. **Proposer Vote Choice**
8. **Start Time**
9. **End Time**
10. **Proposal State**
11. **Voting Choices**
12. **Total Votes**
13. **Total Voting Power**
14. **Top Voter**
15. **Top Voter Power**
16. **Top Voter Percentage**
17. **Outcome**

## Installation

```bash
pip install selenium beautifulsoup4 transformers torch requests
```

You'll also need ChromeDriver installed and in your PATH.

## Usage

### Method 1: Using URL File (Recommended)

1. **Add URLs to `tally_urls.txt`**:
   ```
   https://www.tally.xyz/gov/arbitrum/proposal/[proposal_id]
   https://www.tally.xyz/gov/compound/proposal/[proposal_id]
   # Add more URLs here
   ```

2. **Run the scraper**:
   ```bash
   python tally_scraper_complete.py
   ```

### Method 2: Modify Code Directly

Edit the `urls` list in the `main()` function:

```python
urls = [
    "https://www.tally.xyz/gov/arbitrum/proposal/51852039695020109312343918128899814224888993575448130385109956762385891284115",
    "https://www.tally.xyz/gov/compound/proposal/[another_proposal_id]",
    # Add more URLs
]
```

## Output Files

The scraper generates several output files:

1. **`activist_tally_proposals.csv`** - Filtered activist proposals (CSV format)
2. **`activist_tally_proposals.json`** - Filtered activist proposals (JSON format)
3. **`all_tally_proposals.csv`** - All scraped proposals before filtering (CSV format)
4. **`all_tally_proposals.json`** - All scraped proposals before filtering (JSON format)
5. **`debug_tally.html`** - Last scraped page HTML for debugging

## Activist Filter

The filter uses the same methodology as `scraper2.py`:

1. **Keyword Matching**: Checks for activist-related keywords in proposal title and content
2. **NLP Classification**: Uses BART model for zero-shot classification with 0.6 threshold
3. **Fallback**: If NLP fails, accepts proposals based on keywords only

### Keywords Include:
- Governance changes: "replace", "remove", "elect", "amend constitution"
- Financial control: "withdraw funds", "budget change", "treasury control"
- Power shifts: "change voting power", "remove delegation"
- Project control: "pivot", "terminate project", "halt"
- Legal actions: "lawsuit", "ban", "suspend"

## HTML/CSS Selectors Used

The scraper uses multiple fallback selectors for each data point:

```python
# Example selectors for proposer information
"proposer": [
    '[data-testid*="proposer"]',
    '[data-testid*="author"]',
    '.proposer',
    '.proposal-author'
]
```

## Getting Full HTML

If you need to get the complete HTML of a Tally page for analysis:

```bash
python get_full_html.py
```

This will save the full rendered HTML to `tally_proposal_full.html`.

## Troubleshooting

### Common Issues:

1. **ChromeDriver not found**: Install ChromeDriver and add to PATH
2. **Selenium timeout**: Increase `wait_time` parameter
3. **NLP model download**: First run downloads the BART model (~1.6GB)
4. **Rate limiting**: The scraper includes 3-second delays between requests

### Debug Steps:

1. Check `debug_tally.html` to see the actual page structure
2. Verify URLs are accessible manually
3. Check console output for specific error messages

## Customization

### Adding New Selectors

If Tally changes their HTML structure, update the selector lists in each extraction function:

```python
def extract_proposer(soup):
    selectors = [
        '[data-testid*="proposer"]',  # Add new selectors here
        '.new-proposer-class',
        # ... existing selectors
    ]
```

### Modifying Filter Criteria

To change the activist filter:

1. **Add keywords**: Update the `keywords` list in `filter_before()`
2. **Change NLP threshold**: Modify the `0.6` threshold
3. **Update hypothesis**: Change the classification hypothesis template

## Example Output

```json
{
  "url": "https://www.tally.xyz/gov/arbitrum/proposal/...",
  "dao_name": "Arbitrum",
  "proposal_title": "[CONSTITUTIONAL] Remove Cost Cap, Update Executors...",
  "proposer": "0x1234...abcd",
  "proposer_voting_power": "1,234,567",
  "proposal_state": "Executed",
  "activist_score": 0.85,
  "outcome": "Passed"
}
```

## Performance

- **Speed**: ~30-45 seconds per proposal (including page load and NLP)
- **Memory**: ~2GB RAM (for NLP model)
- **Network**: Respectful 3-second delays between requests
