"""
Debug script to test Tally navigation and identify the data:, issue
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

try:
    from webdriver_manager.chrome import ChromeDriverManager
    WEBDRIVER_MANAGER_AVAILABLE = True
except ImportError:
    WEBDRIVER_MANAGER_AVAILABLE = False

def init_debug_driver():
    """Initialize Chrome driver for debugging"""
    chrome_options = Options()
    # Don't use headless for debugging
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    
    # Add additional options to prevent data:, issues
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-extensions")
    
    if WEBDRIVER_MANAGER_AVAILABLE:
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            print("Using webdriver-manager for ChromeDriver")
            return driver
        except Exception as e:
            print(f"webdriver-manager failed: {e}")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        print("Using system ChromeDriver")
        return driver
    except Exception as e:
        print(f"ChromeDriver error: {e}")
        raise

def debug_tally_navigation():
    """Debug Tally navigation step by step"""
    driver = init_debug_driver()
    
    try:
        print("=== Debugging Tally Navigation ===")
        
        # Step 1: Test basic navigation
        print("\n1. Testing basic navigation to Tally...")
        driver.get("https://www.tally.xyz")
        time.sleep(5)
        
        current_url = driver.current_url
        print(f"Current URL: {current_url}")
        print(f"Page title: {driver.title}")
        
        if "data:" in current_url:
            print("ERROR: Got data URL on basic navigation!")
            return
        
        # Step 2: Navigate to explore page
        print("\n2. Navigating to explore page...")
        driver.get("https://www.tally.xyz/explore")
        time.sleep(10)
        
        current_url = driver.current_url
        print(f"Current URL: {current_url}")
        print(f"Page title: {driver.title}")
        
        if "data:" in current_url:
            print("ERROR: Got data URL on explore page!")
            return
        
        # Step 3: Wait for page to load and find DAO links
        print("\n3. Looking for DAO links...")
        
        # Wait for page to load
        try:
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            print("Page body loaded")
        except:
            print("Timeout waiting for page body")
        
        # Try different selectors for DAO links
        selectors_to_try = [
            "a.chakra-link.css-nuz2iz",
            "a[href*='/gov/']",
            ".chakra-link",
            "a[href*='tally.xyz/gov']"
        ]
        
        for selector in selectors_to_try:
            print(f"\nTrying selector: {selector}")
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            print(f"Found {len(elements)} elements")
            
            for i, element in enumerate(elements[:5]):  # Check first 5
                try:
                    href = element.get_attribute('href')
                    text = element.text.strip()
                    print(f"  {i+1}. href='{href}', text='{text[:30]}...'")
                    
                    if href == "data:,":
                        print(f"    WARNING: Found data:, URL!")
                except Exception as e:
                    print(f"    Error getting attributes: {e}")
        
        # Step 4: Try to click on a DAO link
        print("\n4. Attempting to click on first valid DAO link...")
        
        dao_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='/gov/']")
        valid_links = []
        
        for link in dao_links:
            href = link.get_attribute('href')
            if href and href.startswith('http') and '/gov/' in href and href != 'data:,':
                valid_links.append((link, href))
        
        if valid_links:
            link_element, link_url = valid_links[0]
            print(f"Clicking on: {link_url}")
            
            try:
                link_element.click()
                time.sleep(5)
                
                current_url = driver.current_url
                print(f"After click - Current URL: {current_url}")
                
                if "data:" in current_url:
                    print("ERROR: Got data URL after clicking!")
                else:
                    print("SUCCESS: Navigation worked!")
                    
            except Exception as e:
                print(f"Error clicking link: {e}")
        else:
            print("No valid DAO links found")
        
        print("\n=== Debug Complete ===")
        print("Check the browser window to see the current state")
        input("Press Enter to close the browser...")
        
    except Exception as e:
        print(f"Debug error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_tally_navigation()
