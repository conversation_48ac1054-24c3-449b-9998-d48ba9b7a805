"""
Debug script to test Tally navigation and identify the data:, issue
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

try:
    from webdriver_manager.chrome import ChromeDriverManager
    WEBDRIVER_MANAGER_AVAILABLE = True
except ImportError:
    WEBDRIVER_MANAGER_AVAILABLE = False

def init_debug_driver():
    """Initialize Chrome driver for debugging"""
    chrome_options = Options()
    # Don't use headless for debugging
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    
    # Add additional options to prevent data:, issues
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-extensions")
    
    if WEBDRIVER_MANAGER_AVAILABLE:
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            print("Using webdriver-manager for ChromeDriver")
            return driver
        except Exception as e:
            print(f"webdriver-manager failed: {e}")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        print("Using system ChromeDriver")
        return driver
    except Exception as e:
        print(f"ChromeDriver error: {e}")
        raise

def debug_tally_navigation():
    """Debug Tally navigation step by step"""
    driver = init_debug_driver()
    
    try:
        print("=== Debugging Tally Navigation ===")
        
        # Step 1: Test basic navigation
        print("\n1. Testing basic navigation to Tally...")
        driver.get("https://www.tally.xyz")
        time.sleep(5)
        
        current_url = driver.current_url
        print(f"Current URL: {current_url}")
        print(f"Page title: {driver.title}")
        
        if "data:" in current_url:
            print("ERROR: Got data URL on basic navigation!")
            return
        
        # Step 2: Navigate to explore page
        print("\n2. Navigating to explore page...")
        driver.get("https://www.tally.xyz/explore")
        time.sleep(10)
        
        current_url = driver.current_url
        print(f"Current URL: {current_url}")
        print(f"Page title: {driver.title}")
        
        if "data:" in current_url:
            print("ERROR: Got data URL on explore page!")
            return
        
        # Step 3: Wait for page to load and find DAO links
        print("\n3. Looking for DAO links...")
        
        # Wait for page to load
        try:
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            print("Page body loaded")
        except:
            print("Timeout waiting for page body")
        
        # Try different selectors for DAO links
        selectors_to_try = [
            "a.chakra-link.css-nuz2iz",
            "a[href*='/gov/']",
            ".chakra-link",
            "a[href*='tally.xyz/gov']"
        ]
        
        for selector in selectors_to_try:
            print(f"\nTrying selector: {selector}")
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            print(f"Found {len(elements)} elements")
            
            for i, element in enumerate(elements[:5]):  # Check first 5
                try:
                    href = element.get_attribute('href')
                    text = element.text.strip()
                    print(f"  {i+1}. href='{href}', text='{text[:30]}...'")
                    
                    if href == "data:,":
                        print(f"    WARNING: Found data:, URL!")
                except Exception as e:
                    print(f"    Error getting attributes: {e}")
        
        # Step 4: Test pagination - look for Load More button
        print("\n4. Testing pagination - looking for 'Load More' button...")

        load_more_selectors = [
            "button.chakra-button.css-g8hi89",
            ".chakra-button.css-g8hi89",
            "button[class*='chakra-button'][class*='css-g8hi89']",
            "button:contains('Load more')",
            "button:contains('Show more')"
        ]

        load_more_found = False
        for selector in load_more_selectors:
            try:
                if ":contains(" in selector:
                    # Handle text-based selectors
                    buttons = driver.find_elements(By.TAG_NAME, "button")
                    for btn in buttons:
                        if "load more" in btn.text.lower() or "show more" in btn.text.lower():
                            print(f"Found Load More button with text: '{btn.text}'")
                            load_more_found = True
                            break
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"Found {len(elements)} Load More buttons with selector: {selector}")
                        for i, elem in enumerate(elements):
                            print(f"  Button {i+1}: text='{elem.text}', displayed={elem.is_displayed()}, enabled={elem.is_enabled()}")
                        load_more_found = True
                        break
            except Exception as e:
                print(f"Error with selector {selector}: {e}")

        if not load_more_found:
            print("No Load More button found")

        # Step 5: Count current DAOs before and after scrolling
        print("\n5. Counting DAOs and testing Load More...")

        initial_dao_count = len(driver.find_elements(By.CSS_SELECTOR, "a[href*='/gov/']"))
        print(f"Initial DAO count: {initial_dao_count}")

        # Scroll to bottom
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)

        # Try to click Load More if it exists
        try:
            load_more_button = driver.find_element(By.CSS_SELECTOR, "button.chakra-button.css-g8hi89")
            if load_more_button.is_displayed() and load_more_button.is_enabled():
                print("Clicking Load More button...")
                driver.execute_script("arguments[0].click();", load_more_button)
                time.sleep(5)

                new_dao_count = len(driver.find_elements(By.CSS_SELECTOR, "a[href*='/gov/']"))
                print(f"DAO count after Load More: {new_dao_count}")
                print(f"New DAOs loaded: {new_dao_count - initial_dao_count}")
            else:
                print("Load More button not clickable")
        except Exception as e:
            print(f"Could not click Load More: {e}")

        # Step 6: Try to click on a DAO link
        print("\n6. Attempting to click on first valid DAO link...")

        dao_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='/gov/']")
        valid_links = []

        for link in dao_links:
            href = link.get_attribute('href')
            if href and href.startswith('http') and '/gov/' in href and href != 'data:,':
                valid_links.append((link, href))

        print(f"Found {len(valid_links)} valid DAO links")

        if valid_links:
            link_element, link_url = valid_links[0]
            print(f"Clicking on: {link_url}")

            try:
                link_element.click()
                time.sleep(5)

                current_url = driver.current_url
                print(f"After click - Current URL: {current_url}")

                if "data:" in current_url:
                    print("ERROR: Got data URL after clicking!")
                else:
                    print("SUCCESS: Navigation worked!")

            except Exception as e:
                print(f"Error clicking link: {e}")
        else:
            print("No valid DAO links found")
        
        print("\n=== Debug Complete ===")
        print("Check the browser window to see the current state")
        input("Press Enter to close the browser...")
        
    except Exception as e:
        print(f"Debug error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_tally_navigation()
