[{"url": "https://www.tally.xyz/gov/arbitrum/proposal/94423886836435773843507976898262621297544156552971145658873213763398017341229?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Alternative", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/51852039695020109312343918128899814224888993575448130385109956762385891284115?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Remove Cost Cap on Nova", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/97685288731263391833044854304895851471157040105038894699042975271050068874277?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Disclaimer from L2BEAT", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/37638751032596392177176596241110468090299645534448966767963399982622616318705?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Non-Constitutional", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/71020107401388505040510993373598301285550678565865201408741893567942851985019?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/13050485773425645466153415888358064378805606653363056526521393182333458879398?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "DeFi Renaissance Incentive Program (DRIP)", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/compound/proposal/468?govId=eip155:1:******************************************", "dao_name": "ArbitrumArbitrumActive proposal", "proposal_title": "Proposal summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/compound/proposal/471?govId=eip155:1:******************************************", "dao_name": "ArbitrumArbitrumActive proposal", "proposal_title": "Proposal summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/compound/proposal/470?govId=eip155:1:******************************************", "dao_name": "ArbitrumArbitrumActive proposal", "proposal_title": "Proposal summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/compound/proposal/472?govId=eip155:1:******************************************", "dao_name": "ArbitrumArbitrumActive proposal", "proposal_title": "Simply Summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/compound/proposal/469?govId=eip155:1:******************************************", "dao_name": "ArbitrumArbitrumActive proposal", "proposal_title": "Proposal summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lazy-summer-dao-official/proposal/79475976926147071991437277137357232192970165472633019529787185609577345627805", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Lazy Summer DAO (Official) | SIP2.26.2: Add 8 Arks to LazyVault_LowerRisk_USDC Fleet on arbitrum", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lazy-summer-dao-official/proposal/3145610984927289913539574233848254608805290671896391533891747596281568720690", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Lazy Summer DAO (Official) | SIP2.10: Add Arks to LazyVault_LowerRisk_WETH Fleet", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lazy-summer-dao-official/proposal/55382621484008856270824158507174839126988702593849842902958477306439243670050", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Lazy Summer DAO (Official) | SIP3.8: Governance Staking Rewards extension", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lazy-summer-dao-official/proposal/67800713488613536825958606951667554616428906884148074395805160624508108607986", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Lazy Summer DAO (Official) | SIP2.25.2: Add Arks to LazyVault_LowerRisk_WETH Fleet", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lazy-summer-dao-official/proposal/49266720927551453708418803297931217118929727126907791991479157613215099729167", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Lazy Summer DAO (Official) | SIP5.11: Multi-Chain Fleet Token Transferability Enablement", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/rootstockcollective/proposal/39990707672195051168594575744382419302042846509248048300172543083260826312355", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/rootstockcollective/proposal/1137798816558532096212119790801866456118003917707367950339267312559656835421", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/rootstockcollective/proposal/2807627309598070071667072058548585196227562455088491319004092295000735622022", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/rootstockcollective/proposal/20334724607873707248076103578917452376811310518488125687276809244443587444635", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/rootstockcollective/proposal/3558437576914083594927916072630422390367738946978516722402853242355792114389", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/nounsdao/proposal/848", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Why CFMs Make Sense for Nouns", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/nounsdao/proposal/846", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Nouns Art Contribution Agreement", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/nounsdao/proposal/850", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | <PERSON><PERSON> | Second to None — Korea Web3 Meetup (<PERSON><PERSON> x Tabi)", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/nounsdao/proposal/849", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Nouns Dao | Nouns @ DevConnect 2025", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/nounsdao/proposal/847", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/unlock-protocol/proposal/40472726276015331071354107376870843926615714435692615993085386063299565572826", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/unlock-protocol/proposal/58294980015609654206876136901131507430409704514309342818237981473640699573120", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/unlock-protocol/proposal/94807176600988350661917337667759674222877675691964147270636622991623318480998", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Unlock Protocol DAO Steward Council", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/unlock-protocol/proposal/71093475876174329249872463380367980550397678211446723424879001582531424099528", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Potential Impact", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/unlock-protocol/proposal/28854742609204055320550261733308455864956452599238948209889883877821849355818", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lendefi-dao-mainnet/proposal/112372276415273718314955966155583994529214810677574610637400175305919168000621", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lendefi-dao-mainnet/proposal/57482690335242437047737501284262954401795904386667957455429837352521609031610", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lendefi-dao-mainnet/proposal/60170631279150678173016900821129619086172753112364176024721571585759815315472", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lendefi-dao-mainnet/proposal/10650759190226331751296963824230576516676948180051932339931928204164498064023", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lendefi-dao-mainnet/proposal/70509550638977825120611819981295492997546314029339810972200444277766512405235", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/optimism/proposal/11595556248137084019024264942104169821784001997050285438055123196153848845549", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/optimism/proposal/20327152654308054166942093105443920402082671769027198649343468266910325783863", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/optimism/proposal/85591583404433237270543189567126336043697987369929953414380041066767718361144", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/optimism/proposal/112047914448178129124907599845371359974256142096707624346739203514792263033877", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/optimism/proposal/23645858735980247449510302520189443998369792603230659191221949690963680987113", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/zksync/proposal/14920227315823844313255249182525601975564035647349569740836448589354658768084?govId=eip155:324:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | ZKsync | [TPP-7] ZKsync Guardians Funding 2024-2026", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/zksync/proposal/62921954496791164991088292844527972033503604235062526266434445973055965160650?govId=eip155:324:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "[TEST-TPP] Guardian Veto Rehearsal 2025-Q2", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/zksync/proposal/97689115420129047109255183628089175185608660755000395855946331923921270505453?govId=eip155:324:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "[ZIP-10] Activate ZK Gateway as a Settlement Layer", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/zksync/proposal/38542076628472360665761284306860773162167153028104855759973536253827423667325?govId=eip155:324:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | ZKsync | [TPP-6] ZKsync Security Council v2 Funding ", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/zksync/proposal/103009526770705342015760257902601847378333885610277726776315709127806766289886?govId=eip155:324:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "[TPP-5] ZKsync Security Council Bridge Funding", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/aave/proposal/413", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "title: \"Request for Bounty Payout - December 2023\"\nauthor: \"BGD Labs @bgdlabs\"\ndiscussions: \"https://governance.aave.com/t/bgd-request-for-bounty-payout-december-2023/15826\"", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/aave/proposal/415", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "title: \"Aave Governance V3 Activation Short\"\nauthor: \"BGD Labs (@bgdlabs)\"\ndiscussions: \"https://governance.aave.com/t/bgd-aave-governance-v3-activation-plan/14993/13\"", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/aave/proposal/412", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "title: \"Transfer all CRV positions from Ethereum Mainnet Collector to GLC Safe\"\nauthor: \"TokenLogic & karpatkey\"\ndiscussions: \"https://governance.aave.com/t/arfc-deploy-acrv-crv-to-vecrv/11628\"", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/aave/proposal/414", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "title: \"Treasury Management - Polygon v2 to v3 Migration\"\nauthor: \"TokenLogic\"\ndiscussions: \"https://governance.aave.com/t/arfc-migrate-consolidate-polygon-treasury/12248\"", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/aave/proposal/416", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "title: \"Aave Governance V3 Activation Short\"\nauthor: \"BGD Labs (@bgdlabs)\"\ndiscussions: \"https://governance.aave.com/t/bgd-aave-governance-v3-activation-plan/14993/13\"", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/wormhole/proposal/47429158753237715990542297958862699741222441622333263820239935959960400727574?govId=eip155:1:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | <PERSON><PERSON><PERSON> | WIP-3: Establishment of a Grants Program", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/wormhole/proposal/69892078890661102806744763122017665457499503026694970277189718732036784909161?govId=eip155:1:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Wormhole | WIP-1: Ratification of Code of Conduct for Wormhole Governance", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/wormhole/proposal/85280369263780081188605485888774324166542416516834239378857741400139505237053?govId=eip155:1:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Wormhole | WIP-2: Ratification of Wormhole Governance Proposal Process", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/uniswap/proposal/87", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Part 1: Descriptive Report (Continued)", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/uniswap/proposal/85", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Establish Uniswap v4 Licensing Process", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/uniswap/proposal/89", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Uniswap | Scaling V4 and Supporting Unichain", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/uniswap/proposal/86", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Part 1: Descriptive Report (Continued)", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/uniswap/proposal/88", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scaling V4 and <PERSON> Unich<PERSON>", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/ens/proposal/51903780073989583830128732176342966426696684927524697157008519276797273489808", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | ENS | [Executable] Transfer .locker TLD to Orange Domains LLC", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/ens/proposal/43003012871521685709452092060115706577030630127946926655190871046443785691900", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Description", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/ens/proposal/42524979896803285837776370636134389407867034021879791462477783237030656381157", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Description", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/ens/proposal/20404686300257550242704646761273386459664655640264490428281621095220078268383", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | ENS | [EP 6.13] [Corrected] [Executable] - SPP Season 2 Implementation", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/gitcoin/proposal/5439536612829093937912655931103371047608013142850324150158885844951489661832?govId=eip155:1:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Spinout Precedents", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/gitcoin/proposal/17479554285430824235935339015686957231675135320692618616781702573982249581366?govId=eip155:1:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/gitcoin/proposal/42131400951029541481048442097364639224689659286943854755170681178246078774463?govId=eip155:1:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Gitcoin | [SGTM 004]: Generating Yield on DAO Treasury Assets to Support Runway", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/gitcoin/proposal/108988307804034315231415885432719400721031490539119630109782735447784485014013?govId=eip155:1:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/gitcoin/proposal/12758623331512311890772944556456769209556902913300144815701629851416592697589?govId=eip155:1:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Gitcoin | REPOST [SGTM 004]: Generating Yield on DAO Treasury Assets to Support Runway", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/gmx/proposal/61766673859357430733503697622962588722816780620873012035435332020445962778850", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/gmx/proposal/21013954620510230379424104268150944171944871194668736486285719177296614854851", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Administration of Protocol Owned Liquidity", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/gmx/proposal/90405161706962806984424519053817349280502938378471989506154925296147652728826", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/gmx/proposal/53569830216289886342215258836869785340186708219361279971510739100627020812478", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Administration of Protocol Owned Liquidity", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/gmx/proposal/50604234402203785774253263410835577569148784602180998250634458666190050677642", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/obol/proposal/20181634516748464451610248211159268435895719937634767398559286055659757648365", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Delegate compensation mechanism", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/obol/proposal/100136572429972201092399149325653021919410159214443100676789892534723828331825", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/obol/proposal/29483747347520500834458432109641193798720743379553989524502910046223092148985", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/hop/proposal/51305966013437649942436285826738882927850963257079969027071347095796508382089", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/hop/proposal/51110796908324739060927818333963223981723022081339918858242293266722938698366", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/hop/proposal/111737923756748300705603857254159695288256292303121317158883026653434433048797", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/hop/proposal/87721017444012748515193030446878713568859011267118968862733227740319473259806", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/hop/proposal/100969999281593667256940973842942833194586799619428402879708708448088810673989", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/realtoken-ecosystem-governance/proposal/4424683552151965966882125088217446294173552930844896549941124038029228543900", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/realtoken-ecosystem-governance/proposal/43478084406299074901320149171828820749206418812219485320658354996016720390993", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/realtoken-ecosystem-governance/proposal/5150631514766738506767860160561897034783282967942159502567095037459155940638", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/realtoken-ecosystem-governance/proposal/34149402670799096503362367117662781798718629041345537723321035887926997447023", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/realtoken-ecosystem-governance/proposal/104218136340266855131004601083077576932425791552173586887718875252003955435316", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lisk/proposal/104491616640851060908912969330100781751660696512219643195852309797072404571003", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Executive summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lisk/proposal/50662917188321676120053869612409800610036542089570670229727110785716070950770", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Executive summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lisk/proposal/108962774976382761236411457828288488810120718701128806248655919102812853083573", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Lisk | Builder Program - Strategy Season 1", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lisk/proposal/32814469402399452716516255556117334038308646649824971467378299056369841520770", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Executive summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lisk/proposal/109529319231798472455931112461964493964262461314287853501160335464858704295202", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Lisk | Should 100 million LSK of the DAO treasury be burned?", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/dopeworld/proposal/88702160413698520563166834242603633985091867792854920182260031261751083053092", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/dopeworld/proposal/30885061680448958468182635510344373626527200061737232270074181769209560581737", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/dopeworld/proposal/54204142656134553108992464755737900727946814092613382832861384408764607628402", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "1. Introduction", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/dopeworld/proposal/102002264798274141227122054390376679452839696014247849057047419956957467425631", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/dopeworld/proposal/6450388463021445952368711532766591911978822233918865965355835262640346505613", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/94423886836435773843507976898262621297544156552971145658873213763398017341229?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Alternative", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/51852039695020109312343918128899814224888993575448130385109956762385891284115?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Remove Cost Cap on Nova", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/97685288731263391833044854304895851471157040105038894699042975271050068874277?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Disclaimer from L2BEAT", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/37638751032596392177176596241110468090299645534448966767963399982622616318705?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Non-Constitutional", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/71020107401388505040510993373598301285550678565865201408741893567942851985019?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Scan to connect with one of our mobile apps", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/13050485773425645466153415888358064378805606653363056526521393182333458879398?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "DeFi Renaissance Incentive Program (DRIP)", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/compound/proposal/468?govId=eip155:1:******************************************", "dao_name": "ArbitrumArbitrumActive proposal", "proposal_title": "Proposal summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/compound/proposal/471?govId=eip155:1:******************************************", "dao_name": "ArbitrumArbitrumActive proposal", "proposal_title": "Proposal summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/compound/proposal/470?govId=eip155:1:******************************************", "dao_name": "ArbitrumArbitrumActive proposal", "proposal_title": "Proposal summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/compound/proposal/472?govId=eip155:1:******************************************", "dao_name": "ArbitrumArbitrumActive proposal", "proposal_title": "Simply Summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/compound/proposal/469?govId=eip155:1:******************************************", "dao_name": "ArbitrumArbitrumActive proposal", "proposal_title": "Proposal summary", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lazy-summer-dao-official/proposal/79475976926147071991437277137357232192970165472633019529787185609577345627805", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Lazy Summer DAO (Official) | SIP2.26.2: Add 8 Arks to LazyVault_LowerRisk_USDC Fleet on arbitrum", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lazy-summer-dao-official/proposal/3145610984927289913539574233848254608805290671896391533891747596281568720690", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Lazy Summer DAO (Official) | SIP2.10: Add Arks to LazyVault_LowerRisk_WETH Fleet", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lazy-summer-dao-official/proposal/55382621484008856270824158507174839126988702593849842902958477306439243670050", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Lazy Summer DAO (Official) | SIP3.8: Governance Staking Rewards extension", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lazy-summer-dao-official/proposal/67800713488613536825958606951667554616428906884148074395805160624508108607986", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Lazy Summer DAO (Official) | SIP2.25.2: Add Arks to LazyVault_LowerRisk_WETH Fleet", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}, {"url": "https://www.tally.xyz/gov/lazy-summer-dao-official/proposal/49266720927551453708418803297931217118929727126907791991479157613215099729167", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Tally | Lazy Summer DAO (Official) | SIP5.11: Multi-Chain Fleet Token Transferability Enablement", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A"}]