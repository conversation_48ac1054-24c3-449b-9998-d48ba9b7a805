url,dao_name,proposal_title,proposal_content,proposer,proposer_voting_power,proposer_voting_percentage,proposer_vote_choice,start_time,end_time,proposal_state,voting_choices,total_votes,total_voting_power,top_voter,top_voter_power,top_voter_percentage,outcome,activist_score
https://www.tally.xyz/gov/arbitrum/proposal/51852039695020109312343918128899814224888993575448130385109956762385891284115?govId=eip155:42161:******************************************,CompoundCompoundActive proposal,Remove Cost Cap on Nova,Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan,N/A,N/A,N/A,N/A,N/A,N/A,N/A,['N/A'],N/A,N/A,N/A,N/A,N/A,N/A,0.****************
https://www.tally.xyz/gov/arbitrum/proposal/37638751032596392177176596241110468090299645534448966767963399982622616318705?govId=eip155:42161:******************************************,CompoundCompoundActive proposal,Non-Constitutional,Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan,N/A,N/A,N/A,N/A,N/A,N/A,N/A,['N/A'],N/A,N/A,N/A,N/A,N/A,N/A,0.****************
https://www.tally.xyz/gov/arbitrum/proposal/51852039695020109312343918128899814224888993575448130385109956762385891284115?govId=eip155:42161:******************************************,CompoundCompoundActive proposal,Remove Cost Cap on Nova,Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan,N/A,N/A,N/A,N/A,N/A,N/A,N/A,['N/A'],N/A,N/A,N/A,N/A,N/A,N/A,0.****************
https://www.tally.xyz/gov/arbitrum/proposal/37638751032596392177176596241110468090299645534448966767963399982622616318705?govId=eip155:42161:******************************************,CompoundCompoundActive proposal,Non-Constitutional,Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan,N/A,N/A,N/A,N/A,N/A,N/A,N/A,['N/A'],N/A,N/A,N/A,N/A,N/A,N/A,0.****************
