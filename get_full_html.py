import requests
from bs4 import BeautifulSoup
import json
import time
from urllib.parse import urljoin, urlparse
import csv
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def get_full_html_selenium(url, wait_time=10):
    """
    Get full HTML content using Selenium to handle JavaScript rendering
    """
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # Run in background
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print(f"Loading page: {url}")
        driver.get(url)
        
        # Wait for page to load completely
        time.sleep(wait_time)
        
        # Try to wait for specific Tally elements to load
        try:
            WebDriverWait(driver, 15).until(
                EC.any_of(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid*='proposal']")),
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".proposal")),
                    EC.presence_of_element_located((By.CSS_SELECTOR, "h1")),
                    EC.presence_of_element_located((By.CSS_SELECTOR, "main")),
                )
            )
        except:
            print("Timeout waiting for elements, but continuing...")
        
        # Scroll to load any lazy-loaded content
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)
        driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(1)
        
        # Get the full HTML
        html_content = driver.page_source
        print(f"Retrieved HTML content: {len(html_content)} characters")
        
        return html_content
        
    except Exception as e:
        print(f"Error with Selenium: {e}")
        return None
    finally:
        driver.quit()

def save_full_html(url, filename="full_page.html"):
    """
    Save the full HTML content of a page to a file
    """
    html_content = get_full_html_selenium(url)
    
    if html_content:
        with open(filename, "w", encoding="utf-8") as f:
            f.write(html_content)
        print(f"Full HTML saved to: {filename}")
        return True
    else:
        print("Failed to retrieve HTML content")
        return False

def get_full_html_requests(url):
    """
    Alternative method using requests (for non-JS heavy sites)
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        return response.text
    except requests.RequestException as e:
        print(f"Error fetching {url}: {e}")
        return None

def analyze_html_structure(html_content):
    """
    Analyze the HTML structure to identify potential selectors
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    
    print("=== HTML Structure Analysis ===")
    
    # Find all elements with data-testid attributes
    testid_elements = soup.find_all(attrs={"data-testid": True})
    if testid_elements:
        print(f"\nFound {len(testid_elements)} elements with data-testid:")
        for elem in testid_elements[:10]:  # Show first 10
            print(f"  - {elem.name} with data-testid='{elem.get('data-testid')}'")
    
    # Find all elements with class names containing common keywords
    keywords = ['proposal', 'vote', 'dao', 'governance', 'proposer', 'power', 'time', 'state', 'choice', 'outcome']
    for keyword in keywords:
        elements = soup.find_all(class_=lambda x: x and keyword in x.lower())
        if elements:
            print(f"\nElements with '{keyword}' in class name:")
            for elem in elements[:5]:  # Show first 5
                classes = ' '.join(elem.get('class', []))
                print(f"  - {elem.name}.{classes}")
    
    # Find headings
    headings = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
    if headings:
        print(f"\nFound {len(headings)} headings:")
        for h in headings[:5]:
            text = h.get_text(strip=True)[:50]
            print(f"  - {h.name}: {text}")
    
    return soup

def main():
    """
    Main function to demonstrate usage
    """
    # Example Tally URL
    url = "https://www.tally.xyz/gov/arbitrum/proposal/51852039695020109312343918128899814224888993575448130385109956762385891284115"
    
    print("Getting full HTML content...")
    
    # Method 1: Using Selenium (recommended for dynamic sites)
    if save_full_html(url, "tally_proposal_full.html"):
        print("Successfully saved full HTML using Selenium")
        
        # Analyze the structure
        with open("tally_proposal_full.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        
        analyze_html_structure(html_content)
    
    # Method 2: Using requests (backup method)
    print("\nTrying requests method as backup...")
    html_requests = get_full_html_requests(url)
    if html_requests:
        with open("tally_proposal_requests.html", "w", encoding="utf-8") as f:
            f.write(html_requests)
        print("Saved HTML using requests method")

if __name__ == "__main__":
    main()
