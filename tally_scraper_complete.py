import requests
from bs4 import Beautiful<PERSON>oup
import json
import time
import csv
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from transformers import pipeline

try:
    from webdriver_manager.chrome import ChromeDriverManager
    WEBDRIVER_MANAGER_AVAILABLE = True
except ImportError:
    WEBDRIVER_MANAGER_AVAILABLE = False
    print("webdriver-manager not installed. Install with: pip install webdriver-manager")

def init_driver(headless=True):
    """Initialize Chrome driver with options"""
    chrome_options = Options()
    if headless:
        chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

    # Try to use webdriver-manager for automatic ChromeDriver management
    if WEBDRIVER_MANAGER_AVAILABLE:
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            print("Using webdriver-manager for ChromeDriver")
            return driver
        except Exception as e:
            print(f"webdriver-manager failed: {e}")
            print("Falling back to system ChromeDriver")

    # Fallback to system ChromeDriver
    try:
        driver = webdriver.Chrome(options=chrome_options)
        print("Using system ChromeDriver")
        return driver
    except Exception as e:
        print(f"ChromeDriver error: {e}")
        print("\nPlease install ChromeDriver:")
        print("1. Download from https://chromedriver.chromium.org/")
        print("2. Add to system PATH")
        print("3. Or install webdriver-manager: pip install webdriver-manager")
        raise

def safe_navigate(driver, url, max_retries=3):
    """
    Safely navigate to a URL with retry logic to handle data:, issues
    """
    for attempt in range(max_retries):
        try:
            print(f"Navigating to: {url} (attempt {attempt + 1})")
            driver.get(url)
            time.sleep(5)

            current_url = driver.current_url
            print(f"Current URL: {current_url}")

            # Check for data URL issue
            if current_url == "data:," or "data:" in current_url:
                print(f"WARNING: Got data URL on attempt {attempt + 1}")
                if attempt < max_retries - 1:
                    print("Retrying...")
                    time.sleep(5)
                    continue
                else:
                    print("ERROR: Failed to load page after all retries")
                    return False

            # Check if we're on the right domain
            if "tally.xyz" not in current_url:
                print(f"WARNING: Not on Tally domain. Current: {current_url}")
                if attempt < max_retries - 1:
                    print("Retrying...")
                    time.sleep(5)
                    continue
                else:
                    print("ERROR: Could not reach Tally domain")
                    return False

            print("Successfully navigated to page")
            return True

        except Exception as e:
            print(f"Navigation error on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                print("Retrying...")
                time.sleep(5)
            else:
                print("ERROR: Failed to navigate after all retries")
                return False

    return False

def get_full_html_selenium(url, wait_time=15):
    """Get full HTML content using Selenium"""
    driver = init_driver()

    try:
        print(f"Loading page: {url}")
        driver.get(url)
        time.sleep(wait_time)

        # Scroll to load content
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(2)

        return driver.page_source

    except Exception as e:
        print(f"Error with Selenium: {e}")
        return None
    finally:
        driver.quit()

def automated_tally_scraper():
    """
    Automated scraper following the specified procedure:
    1. Go to https://www.tally.xyz/explore
    2. Loop through all DAOs
    3. For each DAO, loop through all proposals
    4. Extract data from each proposal
    5. Apply filtering
    """
    driver = init_driver(headless=False)  # Keep visible for debugging
    all_proposals = []

    try:
        print("=== Starting Automated Tally Scraper ===")

        # Step 1: Open the explore page
        print("Step 1: Opening https://www.tally.xyz/explore")
        if not safe_navigate(driver, "https://www.tally.xyz/explore"):
            print("ERROR: Could not load Tally explore page")
            return []

        # Wait for page to load and load all DAOs with pagination
        print("Loading all DAOs...")

        # First, scroll to load any lazy-loaded content
        last_height = driver.execute_script("return document.body.scrollHeight")
        scroll_attempts = 0
        max_scroll_attempts = 5

        while scroll_attempts < max_scroll_attempts:
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height
            scroll_attempts += 1

        # Now handle pagination by clicking "Load More" buttons
        load_more_attempts = 0
        max_load_more_attempts = 20  # Prevent infinite loops

        while load_more_attempts < max_load_more_attempts:
            try:
                # Look for the "Load More" button
                load_more_button = driver.find_element(By.CSS_SELECTOR, ".chakra-button.css-g8hi89")

                if load_more_button.is_displayed() and load_more_button.is_enabled():
                    print(f"Found 'Load More' button, clicking... (attempt {load_more_attempts + 1})")

                    # Scroll to the button to make sure it's visible
                    driver.execute_script("arguments[0].scrollIntoView(true);", load_more_button)
                    time.sleep(2)

                    # Click the button
                    driver.execute_script("arguments[0].click();", load_more_button)
                    time.sleep(5)  # Wait for new content to load

                    # Scroll down to load any new lazy content
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(3)

                    load_more_attempts += 1
                else:
                    print("Load More button found but not clickable")
                    break

            except Exception as e:
                print(f"No more 'Load More' button found or error clicking: {e}")
                break

        print(f"Completed loading DAOs after {load_more_attempts} 'Load More' clicks")

        # Step 2: Find all DAO links
        print("Step 2: Finding all DAO links...")
        dao_links = driver.find_elements(By.CSS_SELECTOR, "a.chakra-link.css-nuz2iz")
        dao_urls = []

        for link in dao_links:
            href = link.get_attribute('href')
            if href and href.startswith('http') and '/gov/' in href and href != 'data:,':
                dao_urls.append(href)

        print(f"Found {len(dao_urls)} valid DAO URLs to scrape")

        # Debug: Print first few URLs
        if dao_urls:
            print("Sample DAO URLs:")
            for i, url in enumerate(dao_urls[:3]):
                print(f"  {i+1}. {url}")

        # Step 3: Loop through each DAO
        for dao_index, dao_url in enumerate(dao_urls, 1):
            print(f"\n=== Processing DAO {dao_index}/{len(dao_urls)} ===")
            print(f"DAO URL: {dao_url}")

            try:
                # Navigate to DAO page
                if not safe_navigate(driver, dao_url):
                    print(f"Could not navigate to DAO: {dao_url}")
                    continue

                # Click on "Proposals" tab (class="chakra-text css-0")
                try:
                    proposals_tab = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, ".chakra-text.css-0"))
                    )
                    proposals_tab.click()
                    time.sleep(3)
                    print("Clicked on Proposals tab")
                except Exception as e:
                    print(f"Could not find/click proposals tab: {e}")
                    continue

                # Load all proposals by scrolling
                print("Loading all proposals...")
                last_height = driver.execute_script("return document.body.scrollHeight")
                while True:
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(2)
                    new_height = driver.execute_script("return document.body.scrollHeight")
                    if new_height == last_height:
                        break
                    last_height = new_height

                # Find all proposal links using a more robust approach
                print("Finding proposal links...")

                # Try multiple selectors for proposal links
                proposal_selectors = [
                    "a[href*='/proposal/']",
                    ".chakra-text.css-r5m49",
                    "[data-testid*='proposal']",
                    "a[href*='/gov/'][href*='/proposal/']"
                ]

                proposal_links = []
                for selector in proposal_selectors:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        try:
                            if element.tag_name == 'a':
                                href = element.get_attribute('href')
                            else:
                                # Find parent or child link
                                parent_link = element.find_element(By.XPATH, "./ancestor::a[@href] | .//a[@href]")
                                href = parent_link.get_attribute('href')

                            if href and href.startswith('http') and '/proposal/' in href and href != 'data:,':
                                proposal_links.append(href)
                        except:
                            continue

                # Remove duplicates and filter valid URLs
                proposal_links = list(set([url for url in proposal_links if url and url.startswith('http')]))
                print(f"Found {len(proposal_links)} valid proposal URLs in this DAO")

                # Debug: Print first few proposal URLs
                if proposal_links:
                    print("Sample proposal URLs:")
                    for i, url in enumerate(proposal_links[:3]):
                        print(f"  {i+1}. {url}")

                # Step 4: Loop through each proposal URL and extract data
                for prop_index, proposal_url in enumerate(proposal_links, 1):
                    print(f"\n--- Processing Proposal {prop_index}/{len(proposal_links)} ---")
                    print(f"Proposal URL: {proposal_url}")

                    try:
                        # Navigate directly to the proposal URL using safe navigation
                        if not safe_navigate(driver, proposal_url):
                            print(f"Could not navigate to proposal: {proposal_url}")
                            continue

                        # Extract data from current page
                        proposal_data = extract_proposal_data_from_driver(driver, proposal_url)

                        if proposal_data:
                            all_proposals.append(proposal_data)
                            print(f"Successfully extracted data for: {proposal_data.get('proposal_title', 'Unknown')[:50]}...")
                        else:
                            print("Failed to extract proposal data")

                        # Small delay between proposals
                        time.sleep(2)

                    except Exception as e:
                        print(f"Error processing proposal {proposal_url}: {e}")
                        continue

                print(f"Completed DAO {dao_index}: {len(proposal_links)} proposals processed")

            except Exception as e:
                print(f"Error processing DAO {dao_url}: {e}")
                continue

        print(f"\n=== Scraping Complete ===")
        print(f"Total proposals scraped: {len(all_proposals)}")

        return all_proposals

    except Exception as e:
        print(f"Critical error in automated scraper: {e}")
        return all_proposals

    finally:
        driver.quit()

# =====================
# Snapshot Scraper Integration
# =====================

def fetch_spaces():
    """Fetch all Snapshot spaces"""
    url = "https://hub.snapshot.org/graphql"
    query = "{ spaces(first: 1000) { id } }"
    try:
        response = requests.post(url, json={"query": query})
        return [space["id"] for space in response.json()["data"]["spaces"]]
    except Exception as e:
        print(f"Error fetching Snapshot spaces: {e}")
        return []

def fetch_proposals(space, first=500):
    """Fetch proposals for a specific Snapshot space"""
    url = "https://hub.snapshot.org/graphql"
    query = """
    query Proposals($space: String!, $first: Int!) {
      proposals(first: $first, where: { space: $space }) {
        id title body start end state choices scores author
      }
    }
    """
    try:
        response = requests.post(url, json={"query": query, "variables":{"space":space,"first":first}})
        return response.json()["data"]["proposals"]
    except Exception as e:
        print(f"Error fetching proposals for {space}: {e}")
        return []

def fetch_votes(proposal_id):
    """Fetch votes for a specific proposal"""
    url = "https://hub.snapshot.org/graphql"
    query = """
    query Votes($proposal_id: String!) {
      votes(first: 1000, where:{proposal:$proposal_id}) { voter vp choice }
    }
    """
    try:
        response = requests.post(url, json={"query": query,"variables":{"proposal_id":proposal_id}})
        return response.json()["data"]["votes"]
    except Exception as e:
        print(f"Error fetching votes for {proposal_id}: {e}")
        return []

def is_junk_proposal(title, body):
    """Check if proposal is junk/spam"""
    if not title and not body:
        return True

    junk_keywords = ["test", "testing", "spam", "delete", "ignore"]
    content = (title + " " + body).lower()

    return any(keyword in content for keyword in junk_keywords)

def snapshot_scraper():
    """
    Scrape Snapshot proposals using GraphQL API
    Adapted from scraper2.py
    """
    print("=== Starting Snapshot Scraper ===")

    snapshot_results = []
    dao_spaces = fetch_spaces()
    print(f"Found {len(dao_spaces)} Snapshot spaces. Fetching proposals...")

    for dao_index, dao in enumerate(dao_spaces, 1):
        print(f"\n--- Processing Snapshot DAO {dao_index}/{len(dao_spaces)}: {dao} ---")

        try:
            proposals = fetch_proposals(dao)
            if not proposals:
                print(f"No proposals found for {dao}")
                continue

            print(f"Found {len(proposals)} proposals in {dao}")

            for prop_index, p in enumerate(proposals, 1):
                try:
                    title = p.get("title", "") or ""
                    body = p.get("body", "") or ""

                    # Skip junk proposals
                    if is_junk_proposal(title, body):
                        continue

                    # Get votes for this proposal
                    votes = fetch_votes(p["id"])
                    if len(votes) < 10:  # Reduced threshold for testing
                        continue

                    print(f"  Processing proposal {prop_index}: {title[:50]}... ({len(votes)} votes)")

                    # Calculate metrics
                    total_vp = sum(p["scores"]) if p["scores"] else 0
                    proposer = p["author"]
                    proposer_vp = proposer_choice = 0

                    # Find proposer's vote
                    for v in votes:
                        if v["voter"] == proposer:
                            proposer_vp, proposer_choice = v["vp"], v["choice"]
                            break

                    proposer_vp_pct = (proposer_vp / total_vp * 100) if total_vp > 0 else 0

                    # Find top voter
                    top_voter = top_voter_vp = top_voter_vp_pct = None
                    if votes:
                        top_vote = max(votes, key=lambda x: x["vp"])
                        top_voter, top_voter_vp = top_vote["voter"], top_vote["vp"]
                        top_voter_vp_pct = (top_voter_vp / total_vp * 100) if total_vp > 0 else 0

                    # Determine outcome
                    outcome = "Unknown"
                    if p["scores"] and len(p["scores"]) > 1:
                        top_choice_index = p["scores"].index(max(p["scores"]))
                        outcome = "Passed" if top_choice_index == 0 else "Failed"

                    # Check if proposer aligned with outcome
                    proposer_aligned = None
                    if proposer_choice is not None and outcome != "Unknown":
                        proposer_aligned = (proposer_choice == top_choice_index + 1)

                    # Create proposal data in same format as Tally scraper
                    proposal_data = {
                        "url": f"https://snapshot.org/#/{dao}/proposal/{p['id']}",
                        "dao_name": dao,
                        "proposal_title": title,
                        "proposal_content": body[:500] + "..." if len(body) > 500 else body,
                        "proposer": proposer,
                        "proposer_voting_power": str(proposer_vp),
                        "proposer_voting_percentage": f"{proposer_vp_pct:.2f}%",
                        "proposer_vote_choice": str(proposer_choice) if proposer_choice else "N/A",
                        "start_time": str(p["start"]),
                        "end_time": str(p["end"]),
                        "proposal_state": p["state"],
                        "voting_choices": p["choices"] if p["choices"] else [],
                        "total_votes": str(len(votes)),
                        "total_voting_power": str(total_vp),
                        "top_voter": top_voter or "N/A",
                        "top_voter_power": str(top_voter_vp) if top_voter_vp else "N/A",
                        "top_voter_percentage": f"{top_voter_vp_pct:.2f}%" if top_voter_vp_pct else "N/A",
                        "outcome": outcome,
                        "source": "snapshot"
                    }

                    snapshot_results.append(proposal_data)

                except Exception as e:
                    print(f"    Error processing proposal {prop_index}: {e}")
                    continue

            # Small delay between DAOs
            time.sleep(0.5)

        except Exception as e:
            print(f"Error processing DAO {dao}: {e}")
            continue

    print(f"\n=== Snapshot Scraping Complete ===")
    print(f"Total Snapshot proposals scraped: {len(snapshot_results)}")

    return snapshot_results

def extract_proposal_data_from_driver(driver, url):
    """
    Extract proposal data from the current page using the active driver
    """
    try:
        # Get page source and parse with BeautifulSoup
        html_content = driver.page_source
        soup = BeautifulSoup(html_content, 'html.parser')

        # Extract all the required data points
        proposal_data = {
            'url': url,
            'dao_name': extract_dao_name(soup),
            'proposal_title': extract_proposal_title(soup),
            'proposal_content': extract_proposal_content(soup),
            'proposer': extract_proposer(soup),
            'proposer_voting_power': extract_proposer_voting_power(soup),
            'proposer_voting_percentage': extract_proposer_voting_percentage(soup),
            'proposer_vote_choice': extract_proposer_vote_choice(soup),
            'start_time': extract_start_time(soup),
            'end_time': extract_end_time(soup),
            'proposal_state': extract_proposal_state(soup),
            'voting_choices': extract_voting_choices(soup),
            'total_votes': extract_total_votes(soup),
            'total_voting_power': extract_total_voting_power(soup),
            'top_voter': extract_top_voter(soup),
            'top_voter_power': extract_top_voter_power(soup),
            'top_voter_percentage': extract_top_voter_percentage(soup),
            'outcome': extract_outcome(soup)
        }

        return proposal_data

    except Exception as e:
        print(f"Error extracting data from {url}: {e}")
        return None

def scrape_tally_proposal(url):
    """
    Comprehensive scraper for Tally proposal data
    """
    html_content = get_full_html_selenium(url)
    if not html_content:
        return None
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Save HTML for debugging
    with open("debug_tally.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    proposal_data = {
        'url': url,
        'dao_name': extract_dao_name(soup),
        'proposal_title': extract_proposal_title(soup),
        'proposal_content': extract_proposal_content(soup),
        'proposer': extract_proposer(soup),
        'proposer_voting_power': extract_proposer_voting_power(soup),
        'proposer_voting_percentage': extract_proposer_voting_percentage(soup),
        'proposer_vote_choice': extract_proposer_vote_choice(soup),
        'start_time': extract_start_time(soup),
        'end_time': extract_end_time(soup),
        'proposal_state': extract_proposal_state(soup),
        'voting_choices': extract_voting_choices(soup),
        'total_votes': extract_total_votes(soup),
        'total_voting_power': extract_total_voting_power(soup),
        'top_voter': extract_top_voter(soup),
        'top_voter_power': extract_top_voter_power(soup),
        'top_voter_percentage': extract_top_voter_percentage(soup),
        'outcome': extract_outcome(soup)
    }
    
    return proposal_data

def extract_dao_name(soup):
    """Extract DAO name"""
    selectors = [
        'h1[data-testid*="dao"]',
        '[data-testid*="governance-name"]',
        '.dao-name',
        'nav a[href*="/gov/"]',
        'title'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            if text and len(text) < 50:  # Reasonable DAO name length
                return text
    
    # Extract from URL
    if '/gov/' in soup.find('title').get_text() if soup.find('title') else '':
        title = soup.find('title').get_text()
        if 'Arbitrum' in title:
            return 'Arbitrum'
        parts = title.split('|')
        if len(parts) >= 2:
            return parts[1].strip()
    
    return 'N/A'

def extract_proposal_title(soup):
    """Extract proposal title"""
    selectors = [
        'h1[data-testid*="proposal"]',
        '[data-testid*="proposal-title"]',
        '.proposal-title',
        'h1',
        'h2'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            if text and len(text) > 10:
                return text
    
    # Try meta tags
    meta_title = soup.find('meta', property='og:title')
    if meta_title:
        return meta_title.get('content', 'N/A')
    
    return 'N/A'

def extract_proposal_content(soup):
    """Extract proposal content/body"""
    selectors = [
        '[data-testid*="description"]',
        '[data-testid*="content"]',
        '.proposal-description',
        '.proposal-body',
        '.markdown-content',
        'article',
        '.content'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            if text and len(text) > 50:
                return text[:500] + "..." if len(text) > 500 else text
    
    return 'N/A'

def extract_proposer(soup):
    """Extract proposer information"""
    selectors = [
        '[data-testid*="proposer"]',
        '[data-testid*="author"]',
        '.proposer',
        '.proposal-author',
        '.author'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            # Look for Ethereum address pattern
            if re.match(r'0x[a-fA-F0-9]{40}', text):
                return text
            elif text and len(text) < 100:
                return text
    
    return 'N/A'

def extract_proposer_voting_power(soup):
    """Extract proposer's voting power"""
    selectors = [
        '[data-testid*="proposer"] [data-testid*="power"]',
        '[data-testid*="proposer-power"]',
        '.proposer .voting-power',
        '.proposer-voting-power'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            # Extract numbers from text
            numbers = re.findall(r'[\d,]+\.?\d*', text)
            if numbers:
                return numbers[0]
    
    return 'N/A'

def extract_proposer_voting_percentage(soup):
    """Extract proposer's voting power percentage"""
    selectors = [
        '[data-testid*="proposer"] [data-testid*="percentage"]',
        '[data-testid*="proposer-percentage"]',
        '.proposer .percentage'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            # Look for percentage
            percentage = re.search(r'(\d+\.?\d*)%', text)
            if percentage:
                return percentage.group(1) + '%'
    
    return 'N/A'

def extract_proposer_vote_choice(soup):
    """Extract what the proposer voted for"""
    selectors = [
        '[data-testid*="proposer"] [data-testid*="choice"]',
        '[data-testid*="proposer-vote"]',
        '.proposer .vote-choice'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
    
    return 'N/A'

def extract_start_time(soup):
    """Extract proposal start time"""
    selectors = [
        '[data-testid*="start"]',
        '[data-testid*="voting-start"]',
        '.start-time',
        '.voting-period .start'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
    
    return 'N/A'

def extract_end_time(soup):
    """Extract proposal end time"""
    selectors = [
        '[data-testid*="end"]',
        '[data-testid*="voting-end"]',
        '.end-time',
        '.voting-period .end'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
    
    return 'N/A'

def extract_proposal_state(soup):
    """Extract proposal state (Active, Passed, Failed, etc.)"""
    selectors = [
        '[data-testid*="status"]',
        '[data-testid*="state"]',
        '.proposal-status',
        '.status-badge',
        '.state'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
    
    return 'N/A'

def extract_voting_choices(soup):
    """Extract voting choices/options"""
    selectors = [
        '[data-testid*="choice"]',
        '[data-testid*="option"]',
        '.vote-option',
        '.voting-choice',
        '.choice'
    ]
    
    choices = []
    for selector in selectors:
        elements = soup.select(selector)
        for elem in elements:
            text = elem.get_text(strip=True)
            if text and text not in choices:
                choices.append(text)
    
    return choices if choices else ['N/A']

def extract_total_votes(soup):
    """Extract total number of votes"""
    selectors = [
        '[data-testid*="total-votes"]',
        '[data-testid*="vote-count"]',
        '.total-votes',
        '.vote-count'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            numbers = re.findall(r'[\d,]+', text)
            if numbers:
                return numbers[0]
    
    return 'N/A'

def extract_total_voting_power(soup):
    """Extract total voting power"""
    selectors = [
        '[data-testid*="total-power"]',
        '[data-testid*="total-voting-power"]',
        '.total-voting-power',
        '.total-power'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            numbers = re.findall(r'[\d,]+\.?\d*', text)
            if numbers:
                return numbers[0]
    
    return 'N/A'

def extract_top_voter(soup):
    """Extract top voter information"""
    selectors = [
        '[data-testid*="top-voter"]',
        '.top-voter',
        '.voters .voter:first-child',
        '.vote-list .vote:first-child'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
    
    return 'N/A'

def extract_top_voter_power(soup):
    """Extract top voter's voting power"""
    selectors = [
        '[data-testid*="top-voter"] [data-testid*="power"]',
        '.top-voter .power',
        '.voters .voter:first-child .power'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            numbers = re.findall(r'[\d,]+\.?\d*', text)
            if numbers:
                return numbers[0]
    
    return 'N/A'

def extract_top_voter_percentage(soup):
    """Extract top voter's voting power percentage"""
    selectors = [
        '[data-testid*="top-voter"] [data-testid*="percentage"]',
        '.top-voter .percentage',
        '.voters .voter:first-child .percentage'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            percentage = re.search(r'(\d+\.?\d*)%', text)
            if percentage:
                return percentage.group(1) + '%'
    
    return 'N/A'

def extract_outcome(soup):
    """Extract proposal outcome"""
    selectors = [
        '[data-testid*="outcome"]',
        '[data-testid*="result"]',
        '.outcome',
        '.result',
        '.proposal-result'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
    
    # Try to infer from state
    state = extract_proposal_state(soup)
    if state.lower() in ['passed', 'executed', 'succeeded']:
        return 'Passed'
    elif state.lower() in ['failed', 'defeated', 'rejected']:
        return 'Failed'
    
    return 'N/A'

def filter_before(proposals):
    """
    Filter proposals based on activist keywords and NLP classification
    Adapted from scraper2.py
    """
    accepted_proposals = []
    keywords = [
        "replace","remove","oust","vote out","impeach","recall","fire",
        "elect","appoint","nominate","re-elect","install","sack",
        "amend","update charter","bylaw","constitution","modify rules",
        "change rules","adjust policy","alter policy","policy change",
        "revise terms","ratify","motion to change",
        "withdraw funds","transfer funds","fund allocation","reallocate budget",
        "budget change","treasury control","redirect funds","cut funding","fund freeze",
        "investigate","audit","review","accountability","watchdog","reporting",
        "oversight","remove steward","replace facilitator",
        "quorum","voting threshold","change voting power","delegate removal",
        "remove delegation","change voting rights","adjust voting weight",
        "pivot","terminate project","halt","pause","sunset","wind down",
        "stop funding","cancel","scrap","shut down","cease operations",
        "lawsuit","litigation","legal","ban","blacklist","suspend","penalize",
        "take control","seize","reclaim","redeploy","change leadership",
        "power shift","redistribute power"
    ]

    # Initialize the classifier
    try:
        classifier = pipeline("zero-shot-classification", model="facebook/bart-large-mnli")
    except Exception as e:
        print(f"Warning: Could not load NLP model: {e}")
        print("Falling back to keyword-only filtering")
        classifier = None

    for proposal in proposals:
        # Get the proposal content for filtering
        content = proposal.get("proposal_content", "") + " " + proposal.get("proposal_title", "")

        # Check if any keywords are present
        if any(keyword.lower() in content.lower() for keyword in keywords):
            print(f"Keyword match found in proposal: {proposal.get('proposal_title', 'Unknown')[:50]}...")

            # If NLP classifier is available, use it for additional validation
            if classifier and content.strip():
                try:
                    labels = ["activist", "non-activist"]
                    hypothesis = "This proposal is {} because it involves significant activism, such as changing governance structure, reallocating treasury funds, acquiring or merging with another DAO, enforcing the use of an external product, or influencing strategic control"

                    result = classifier(content, candidate_labels=labels, hypothesis_template=hypothesis)

                    # Check if the activist score is >= 0.6
                    activist_score = result['scores'][result['labels'].index('activist')]
                    if activist_score >= 0.6:
                        proposal['activist_score'] = activist_score
                        accepted_proposals.append(proposal)
                        print(f"  -> Accepted with activist score: {activist_score:.3f}")
                    else:
                        print(f"  -> Rejected with activist score: {activist_score:.3f}")

                except Exception as e:
                    print(f"  -> NLP classification failed: {e}, accepting based on keywords only")
                    proposal['activist_score'] = 'keyword_only'
                    accepted_proposals.append(proposal)
            else:
                # No NLP classifier, accept based on keywords only
                proposal['activist_score'] = 'keyword_only'
                accepted_proposals.append(proposal)
                print(f"  -> Accepted based on keywords only")
        else:
            print(f"No keyword match in proposal: {proposal.get('proposal_title', 'Unknown')[:50]}...")

    print(f"\nFiltering complete: {len(accepted_proposals)}/{len(proposals)} proposals accepted")
    return accepted_proposals

def filter_after(proposals):
    """
    Advanced filtering using DistilBERT for fine-tuned activist classification
    This is applied after filter_before when there's enough data
    """
    if len(proposals) < 10:
        print("Not enough proposals for filter_after, skipping advanced filtering")
        return proposals

    try:
        from transformers import DistilBertTokenizer, DistilBertForSequenceClassification
        import torch

        print(f"Applying advanced DistilBERT filtering to {len(proposals)} proposals...")

        # Initialize tokenizer and model
        tokenizer = DistilBertTokenizer.from_pretrained("distilbert-base-uncased")
        model = DistilBertForSequenceClassification.from_pretrained("distilbert-base-uncased", num_labels=2)

        accepted_proposals = []

        for i, proposal in enumerate(proposals, 1):
            try:
                # Combine title and content for classification
                content = f"{proposal.get('proposal_title', '')} {proposal.get('proposal_content', '')}"

                if not content.strip():
                    print(f"Proposal {i}: No content to classify, skipping")
                    continue

                # Tokenize
                encodings = tokenizer(
                    content,
                    truncation=True,
                    padding=True,
                    max_length=512,
                    return_tensors="pt"
                )

                # Get model predictions
                with torch.no_grad():
                    outputs = model(**encodings)
                    predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
                    activist_score = predictions[0][1].item()  # Index 1 for activist class

                print(f"Proposal {i}: DistilBERT activist score: {activist_score:.3f}")

                # Accept if activist score > 0.5
                if activist_score > 0.5:
                    proposal['distilbert_score'] = activist_score
                    accepted_proposals.append(proposal)
                    print(f"  -> Accepted (score: {activist_score:.3f})")
                else:
                    print(f"  -> Rejected (score: {activist_score:.3f})")

            except Exception as e:
                print(f"Error processing proposal {i} with DistilBERT: {e}")
                # If error, keep the proposal (fail-safe)
                proposal['distilbert_score'] = 'error'
                accepted_proposals.append(proposal)

        print(f"\nAdvanced filtering complete: {len(accepted_proposals)}/{len(proposals)} proposals accepted")
        return accepted_proposals

    except ImportError as e:
        print(f"Could not import required libraries for filter_after: {e}")
        print("Falling back to filter_before results only")
        return proposals
    except Exception as e:
        print(f"Error in filter_after: {e}")
        print("Falling back to filter_before results only")
        return proposals

def save_to_csv(data_list, filename='tally_proposals.csv'):
    """Save scraped data to CSV"""
    if not data_list:
        return

    fieldnames = data_list[0].keys()

    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data_list)

def scrape_multiple_tally_proposals(urls):
    """
    Scrape multiple Tally proposals and apply filtering
    """
    all_proposals = []

    for i, url in enumerate(urls, 1):
        print(f"\n=== Scraping proposal {i}/{len(urls)} ===")
        print(f"URL: {url}")

        proposal_data = scrape_tally_proposal(url)

        if proposal_data:
            all_proposals.append(proposal_data)
            print(f"Successfully scraped: {proposal_data.get('proposal_title', 'Unknown')[:50]}...")
        else:
            print("Failed to scrape this proposal")

        # Be respectful - add delay between requests
        if i < len(urls):
            print("Waiting 3 seconds before next request...")
            time.sleep(3)

    return all_proposals

def load_urls_from_file(filename="tally_urls.txt"):
    """
    Load URLs from a text file (one URL per line)
    """
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        print(f"Loaded {len(urls)} URLs from {filename}")
        return urls
    except FileNotFoundError:
        print(f"URL file {filename} not found, using default URLs")
        return []

def main():
    """Main function with automated scraping and filtering"""
    print("=== Automated DAO Scraper with Activist Filtering ===")
    print("This will scrape proposals from both Tally and Snapshot")

    # Ask user what to scrape
    print("\nChoose what to scrape:")
    print("1. Tally only (automated navigation)")
    print("2. Snapshot only (GraphQL API)")
    print("3. Both Tally and Snapshot")

    choice = input("Enter choice (1, 2, or 3): ").strip()

    all_proposals = []

    if choice in ["1", "3"]:
        # Ask user for confirmation for Tally scraping
        response = input("\nTally scraping will take several hours. Proceed? (y/n): ").lower().strip()
        if response == 'y':
            print("\nStarting Tally automated scraping...")
            tally_proposals = automated_tally_scraper()
            all_proposals.extend(tally_proposals)
        else:
            print("Tally scraping cancelled.")

    if choice in ["2", "3"]:
        print("\nStarting Snapshot scraping...")
        snapshot_proposals = snapshot_scraper()
        all_proposals.extend(snapshot_proposals)

    if all_proposals:
        print(f"\n=== Successfully scraped {len(all_proposals)} proposal(s) ===")

        # Separate by source
        tally_proposals = [p for p in all_proposals if p.get('source') != 'snapshot']
        snapshot_proposals = [p for p in all_proposals if p.get('source') == 'snapshot']

        print(f"  - Tally proposals: {len(tally_proposals)}")
        print(f"  - Snapshot proposals: {len(snapshot_proposals)}")

        # Save all scraped data (before filtering)
        save_to_csv(all_proposals, 'all_dao_proposals.csv')
        with open('all_dao_proposals.json', 'w', encoding='utf-8') as f:
            json.dump(all_proposals, f, indent=2, ensure_ascii=False)
        print(f"All scraped proposals saved to all_dao_proposals.csv/json")

        # Apply the first filter (filter_before)
        print("\n=== Applying First Filter (Keywords + NLP) ===")
        filtered_proposals_stage1 = filter_before(all_proposals)

        if filtered_proposals_stage1:
            print(f"\n=== Stage 1: {len(filtered_proposals_stage1)} proposals passed initial filter ===")

            # Save stage 1 results
            save_to_csv(filtered_proposals_stage1, 'stage1_filtered_dao_proposals.csv')
            with open('stage1_filtered_dao_proposals.json', 'w', encoding='utf-8') as f:
                json.dump(filtered_proposals_stage1, f, indent=2, ensure_ascii=False)

            # Apply the second filter (filter_after) if we have enough data
            print("\n=== Applying Second Filter (DistilBERT) ===")
            final_filtered_proposals = filter_after(filtered_proposals_stage1)

            if final_filtered_proposals:
                print(f"\n=== Final Results: {len(final_filtered_proposals)} Activist Proposal(s) ===")

                # Separate final results by source
                final_tally = [p for p in final_filtered_proposals if p.get('source') != 'snapshot']
                final_snapshot = [p for p in final_filtered_proposals if p.get('source') == 'snapshot']

                print(f"  - Tally activist proposals: {len(final_tally)}")
                print(f"  - Snapshot activist proposals: {len(final_snapshot)}")

                # Display final results
                for i, proposal in enumerate(final_filtered_proposals, 1):
                    print(f"\n--- Final Activist Proposal {i} ---")
                    print(f"Source: {proposal.get('source', 'tally').upper()}")
                    print(f"DAO: {proposal.get('dao_name', 'N/A')}")
                    print(f"Title: {proposal.get('proposal_title', 'N/A')[:100]}...")
                    print(f"Proposer: {proposal.get('proposer', 'N/A')}")
                    print(f"State: {proposal.get('proposal_state', 'N/A')}")
                    print(f"Stage 1 Score: {proposal.get('activist_score', 'N/A')}")
                    print(f"Stage 2 Score: {proposal.get('distilbert_score', 'N/A')}")
                    print(f"URL: {proposal.get('url', 'N/A')}")

                # Save final filtered results
                save_to_csv(final_filtered_proposals, 'final_activist_dao_proposals.csv')
                with open('final_activist_dao_proposals.json', 'w', encoding='utf-8') as f:
                    json.dump(final_filtered_proposals, f, indent=2, ensure_ascii=False)

                print(f"\n=== Final Results Saved ===")
                print("Final activist proposals saved to:")
                print("- final_activist_dao_proposals.csv")
                print("- final_activist_dao_proposals.json")

                print(f"\nSummary:")
                print(f"- Total scraped: {len(all_proposals)} ({len(tally_proposals)} Tally + {len(snapshot_proposals)} Snapshot)")
                print(f"- Stage 1 filtered: {len(filtered_proposals_stage1)}")
                print(f"- Final filtered: {len(final_filtered_proposals)} ({len(final_tally)} Tally + {len(final_snapshot)} Snapshot)")

            else:
                print("\nNo proposals passed the second filter.")

        else:
            print("\nNo activist proposals found after first filter.")

    else:
        print("No proposals were successfully scraped")

def manual_url_scraping():
    """Alternative function for manual URL-based scraping"""
    print("=== Manual URL-based Scraping ===")

    # Try to load URLs from file first, then fall back to default
    urls = load_urls_from_file("tally_urls.txt")

    if not urls:
        # Default Tally URLs - add more URLs here
        urls = [
            "https://www.tally.xyz/gov/arbitrum/proposal/51852039695020109312343918128899814224888993575448130385109956762385891284115",
            # Add more Tally proposal URLs here
        ]
        print("Using default URLs")

    print(f"Scraping {len(urls)} proposal(s)...")

    # Scrape all proposals
    all_proposals = scrape_multiple_tally_proposals(urls)

    if all_proposals:
        print(f"\n=== Successfully scraped {len(all_proposals)} proposal(s) ===")

        # Apply filtering stages
        print("\n=== Applying Filters ===")
        filtered_stage1 = filter_before(all_proposals)

        if filtered_stage1:
            final_filtered = filter_after(filtered_stage1)

            # Save results
            save_to_csv(all_proposals, 'all_manual_proposals.csv')
            save_to_csv(final_filtered, 'filtered_manual_proposals.csv')

            print(f"Results: {len(final_filtered)}/{len(all_proposals)} proposals are activist")
        else:
            print("No activist proposals found")
    else:
        print("No proposals were successfully scraped")

if __name__ == "__main__":
    print("=== DAO Proposal Scraper with Activist Filtering ===")
    print("Choose scraping method:")
    print("1. Automated scraping (Tally + Snapshot)")
    print("2. Manual URL scraping (Tally URLs from file)")

    choice = input("Enter choice (1 or 2): ").strip()

    if choice == "1":
        main()  # Automated scraping with both sources
    elif choice == "2":
        manual_url_scraping()  # Manual URL scraping
    else:
        print("Invalid choice. Exiting.")
        exit(1)
