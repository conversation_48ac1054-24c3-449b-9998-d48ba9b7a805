import requests
from bs4 import Beautiful<PERSON>oup
import json
import time
import csv
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def get_full_html_selenium(url, wait_time=15):
    """Get full HTML content using Selenium"""
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print(f"Loading page: {url}")
        driver.get(url)
        time.sleep(wait_time)
        
        # Scroll to load content
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(2)
        
        return driver.page_source
        
    except Exception as e:
        print(f"Error with Selenium: {e}")
        return None
    finally:
        driver.quit()

def scrape_tally_proposal(url):
    """
    Comprehensive scraper for Tally proposal data
    """
    html_content = get_full_html_selenium(url)
    if not html_content:
        return None
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Save HTML for debugging
    with open("debug_tally.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    proposal_data = {
        'url': url,
        'dao_name': extract_dao_name(soup),
        'proposal_title': extract_proposal_title(soup),
        'proposal_content': extract_proposal_content(soup),
        'proposer': extract_proposer(soup),
        'proposer_voting_power': extract_proposer_voting_power(soup),
        'proposer_voting_percentage': extract_proposer_voting_percentage(soup),
        'proposer_vote_choice': extract_proposer_vote_choice(soup),
        'start_time': extract_start_time(soup),
        'end_time': extract_end_time(soup),
        'proposal_state': extract_proposal_state(soup),
        'voting_choices': extract_voting_choices(soup),
        'total_votes': extract_total_votes(soup),
        'total_voting_power': extract_total_voting_power(soup),
        'top_voter': extract_top_voter(soup),
        'top_voter_power': extract_top_voter_power(soup),
        'top_voter_percentage': extract_top_voter_percentage(soup),
        'outcome': extract_outcome(soup)
    }
    
    return proposal_data

def extract_dao_name(soup):
    """Extract DAO name"""
    selectors = [
        'h1[data-testid*="dao"]',
        '[data-testid*="governance-name"]',
        '.dao-name',
        'nav a[href*="/gov/"]',
        'title'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            if text and len(text) < 50:  # Reasonable DAO name length
                return text
    
    # Extract from URL
    if '/gov/' in soup.find('title').get_text() if soup.find('title') else '':
        title = soup.find('title').get_text()
        if 'Arbitrum' in title:
            return 'Arbitrum'
        parts = title.split('|')
        if len(parts) >= 2:
            return parts[1].strip()
    
    return 'N/A'

def extract_proposal_title(soup):
    """Extract proposal title"""
    selectors = [
        'h1[data-testid*="proposal"]',
        '[data-testid*="proposal-title"]',
        '.proposal-title',
        'h1',
        'h2'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            if text and len(text) > 10:
                return text
    
    # Try meta tags
    meta_title = soup.find('meta', property='og:title')
    if meta_title:
        return meta_title.get('content', 'N/A')
    
    return 'N/A'

def extract_proposal_content(soup):
    """Extract proposal content/body"""
    selectors = [
        '[data-testid*="description"]',
        '[data-testid*="content"]',
        '.proposal-description',
        '.proposal-body',
        '.markdown-content',
        'article',
        '.content'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            if text and len(text) > 50:
                return text[:500] + "..." if len(text) > 500 else text
    
    return 'N/A'

def extract_proposer(soup):
    """Extract proposer information"""
    selectors = [
        '[data-testid*="proposer"]',
        '[data-testid*="author"]',
        '.proposer',
        '.proposal-author',
        '.author'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            # Look for Ethereum address pattern
            if re.match(r'0x[a-fA-F0-9]{40}', text):
                return text
            elif text and len(text) < 100:
                return text
    
    return 'N/A'

def extract_proposer_voting_power(soup):
    """Extract proposer's voting power"""
    selectors = [
        '[data-testid*="proposer"] [data-testid*="power"]',
        '[data-testid*="proposer-power"]',
        '.proposer .voting-power',
        '.proposer-voting-power'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            # Extract numbers from text
            numbers = re.findall(r'[\d,]+\.?\d*', text)
            if numbers:
                return numbers[0]
    
    return 'N/A'

def extract_proposer_voting_percentage(soup):
    """Extract proposer's voting power percentage"""
    selectors = [
        '[data-testid*="proposer"] [data-testid*="percentage"]',
        '[data-testid*="proposer-percentage"]',
        '.proposer .percentage'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            # Look for percentage
            percentage = re.search(r'(\d+\.?\d*)%', text)
            if percentage:
                return percentage.group(1) + '%'
    
    return 'N/A'

def extract_proposer_vote_choice(soup):
    """Extract what the proposer voted for"""
    selectors = [
        '[data-testid*="proposer"] [data-testid*="choice"]',
        '[data-testid*="proposer-vote"]',
        '.proposer .vote-choice'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
    
    return 'N/A'

def extract_start_time(soup):
    """Extract proposal start time"""
    selectors = [
        '[data-testid*="start"]',
        '[data-testid*="voting-start"]',
        '.start-time',
        '.voting-period .start'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
    
    return 'N/A'

def extract_end_time(soup):
    """Extract proposal end time"""
    selectors = [
        '[data-testid*="end"]',
        '[data-testid*="voting-end"]',
        '.end-time',
        '.voting-period .end'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
    
    return 'N/A'

def extract_proposal_state(soup):
    """Extract proposal state (Active, Passed, Failed, etc.)"""
    selectors = [
        '[data-testid*="status"]',
        '[data-testid*="state"]',
        '.proposal-status',
        '.status-badge',
        '.state'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
    
    return 'N/A'

def extract_voting_choices(soup):
    """Extract voting choices/options"""
    selectors = [
        '[data-testid*="choice"]',
        '[data-testid*="option"]',
        '.vote-option',
        '.voting-choice',
        '.choice'
    ]
    
    choices = []
    for selector in selectors:
        elements = soup.select(selector)
        for elem in elements:
            text = elem.get_text(strip=True)
            if text and text not in choices:
                choices.append(text)
    
    return choices if choices else ['N/A']

def extract_total_votes(soup):
    """Extract total number of votes"""
    selectors = [
        '[data-testid*="total-votes"]',
        '[data-testid*="vote-count"]',
        '.total-votes',
        '.vote-count'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            numbers = re.findall(r'[\d,]+', text)
            if numbers:
                return numbers[0]
    
    return 'N/A'

def extract_total_voting_power(soup):
    """Extract total voting power"""
    selectors = [
        '[data-testid*="total-power"]',
        '[data-testid*="total-voting-power"]',
        '.total-voting-power',
        '.total-power'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            numbers = re.findall(r'[\d,]+\.?\d*', text)
            if numbers:
                return numbers[0]
    
    return 'N/A'

def extract_top_voter(soup):
    """Extract top voter information"""
    selectors = [
        '[data-testid*="top-voter"]',
        '.top-voter',
        '.voters .voter:first-child',
        '.vote-list .vote:first-child'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
    
    return 'N/A'

def extract_top_voter_power(soup):
    """Extract top voter's voting power"""
    selectors = [
        '[data-testid*="top-voter"] [data-testid*="power"]',
        '.top-voter .power',
        '.voters .voter:first-child .power'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            numbers = re.findall(r'[\d,]+\.?\d*', text)
            if numbers:
                return numbers[0]
    
    return 'N/A'

def extract_top_voter_percentage(soup):
    """Extract top voter's voting power percentage"""
    selectors = [
        '[data-testid*="top-voter"] [data-testid*="percentage"]',
        '.top-voter .percentage',
        '.voters .voter:first-child .percentage'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            text = element.get_text(strip=True)
            percentage = re.search(r'(\d+\.?\d*)%', text)
            if percentage:
                return percentage.group(1) + '%'
    
    return 'N/A'

def extract_outcome(soup):
    """Extract proposal outcome"""
    selectors = [
        '[data-testid*="outcome"]',
        '[data-testid*="result"]',
        '.outcome',
        '.result',
        '.proposal-result'
    ]
    
    for selector in selectors:
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
    
    # Try to infer from state
    state = extract_proposal_state(soup)
    if state.lower() in ['passed', 'executed', 'succeeded']:
        return 'Passed'
    elif state.lower() in ['failed', 'defeated', 'rejected']:
        return 'Failed'
    
    return 'N/A'

def save_to_csv(data_list, filename='tally_proposals.csv'):
    """Save scraped data to CSV"""
    if not data_list:
        return
    
    fieldnames = data_list[0].keys()
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data_list)

def main():
    """Main function"""
    # Example Tally URL
    url = "https://www.tally.xyz/gov/arbitrum/proposal/51852039695020109312343918128899814224888993575448130385109956762385891284115"
    
    print("Scraping Tally proposal...")
    proposal_data = scrape_tally_proposal(url)
    
    if proposal_data:
        print("\n=== Scraped Data ===")
        for key, value in proposal_data.items():
            print(f"{key}: {value}")
        
        # Save to files
        save_to_csv([proposal_data])
        
        with open('tally_proposal.json', 'w', encoding='utf-8') as f:
            json.dump(proposal_data, f, indent=2, ensure_ascii=False)
        
        print("\nData saved to tally_proposals.csv and tally_proposal.json")
    else:
        print("Failed to scrape proposal data")

if __name__ == "__main__":
    main()
