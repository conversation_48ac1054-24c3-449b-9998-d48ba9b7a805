[{"url": "https://www.tally.xyz/gov/arbitrum/proposal/51852039695020109312343918128899814224888993575448130385109956762385891284115?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Remove Cost Cap on Nova", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A", "activist_score": 0.****************}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/37638751032596392177176596241110468090299645534448966767963399982622616318705?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Non-Constitutional", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A", "activist_score": 0.****************}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/51852039695020109312343918128899814224888993575448130385109956762385891284115?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Remove Cost Cap on Nova", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A", "activist_score": 0.****************}, {"url": "https://www.tally.xyz/gov/arbitrum/proposal/37638751032596392177176596241110468090299645534448966767963399982622616318705?govId=eip155:42161:******************************************", "dao_name": "CompoundCompoundActive proposal", "proposal_title": "Non-Constitutional", "proposal_content": "Scan to connect with one of our mobile appsCoinbase Wallet appConnect with your self-custody walletCoinbase appConnect with your Coinbase accountOpen Coinbase Wallet appTapScan", "proposer": "N/A", "proposer_voting_power": "N/A", "proposer_voting_percentage": "N/A", "proposer_vote_choice": "N/A", "start_time": "N/A", "end_time": "N/A", "proposal_state": "N/A", "voting_choices": ["N/A"], "total_votes": "N/A", "total_voting_power": "N/A", "top_voter": "N/A", "top_voter_power": "N/A", "top_voter_percentage": "N/A", "outcome": "N/A", "activist_score": 0.****************}]