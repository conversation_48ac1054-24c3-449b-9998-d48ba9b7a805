from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pandas as pd
from transformers import pipeline
import time
from transformers import DistilBertTokenizer
from transformers import DistilBertForSequenceClassification

def init_driver():
  path = "C:\\Program Files (x86)\\chromedriver.exe"
  service = Service(path)
  driver = webdriver.Chrome(service=service)
  return driver

def deep_scraper():  #FINISH LATER, HAVE TO GET THE VOTES AND PROPOSER NAME AND HIS G SCORE AND HIS TOKENS, USE COINSTATS FOR THE TOKENS PART
  driver = init_driver()
  driver.get("https://deepdao.io/daofeed?organizations=&people=")
  info = []
  elements = driver.find_elements(By.CLASS_NAME, "infinite-scroll-component styles_cardsContainer__3v7HE")
  for x in elements:
    button = driver.find_element(By.CLASS_NAME, "styles_title__3A0E-")
    button.click()
    content = x.find_element(By.CLASS_NAME, "styles_title__2pkpt")
    text_content = content.text
    proposer = x.find_element()
    info.append(
        {"body": (text_content)}
        {"Proposer": }
    driver.back()
  return info

def tally_scraper(): #https://www.tally.xyz/explore
      #CODE HERE
def fetch_spaces():
    url = "https://hub.snapshot.org/graphql"
    query = "{ spaces(first: 1000) { id } }"
    response = requests.post(url, json={"query": query})
    return [space["id"] for space in response.json()["data"]["spaces"]]

def fetch_proposals(space, first=500):
    url = "https://hub.snapshot.org/graphql"
    query = """
    query Proposals($space: String!, $first: Int!) {
      proposals(first: $first, where: { space: $space }) {
        id title body start end state choices scores author
      }
    }
    """
    response = requests.post(url, json={"query": query, "variables":{"space":space,"first":first}})
    try: return response.json()["data"]["proposals"]
    except: return []

def fetch_votes(proposal_id):
    url = "https://hub.snapshot.org/graphql"
    query = """
    query Votes($proposal_id: String!) {
      votes(first: 1000, where:{proposal:$proposal_id}) { voter vp choice }
    }
    """
    response = requests.post(url, json={"query": query,"variables":{"proposal_id":proposal_id}})
    try: return response.json()["data"]["votes"]
    except: return []

# =====================
# Snapshot Scraper
# =====================
snapshot_results = []
dao_spaces = fetch_spaces()
print(f"Found {len(dao_spaces)} Snapshot spaces. Fetching proposals...")
'''
for dao in dao_spaces:
    proposals = fetch_proposals(dao)
    titles = [p["title"] or "" for p in proposals]
    bodies = [p["body"] or "" for p in proposals]

    # Batch NLP
    texts = [t + " " + b for t, b in zip(titles, bodies)]
    keyword_hits = [is_activist_keywords(t,b) for t,b in zip(titles,bodies)]
    nlp_results = batch_activist_nlp(texts, threshold=0.6)

    for p, title, body, keyword_hit, nlp_detected in zip(proposals, titles, bodies, keyword_hits, nlp_results):
        if is_junk_proposal(title, body): continue
        activist_detected = keyword_hit or nlp_detected
        if not activist_detected: continue

        votes = fetch_votes(p["id"])
        if len(votes) < 300: continue

        total_vp = sum(p["scores"]) if p["scores"] else 0
        proposer = p["author"]
        proposer_vp = proposer_choice = 0
        for v in votes:
            if v["voter"] == proposer:
                proposer_vp, proposer_choice = v["vp"], v["choice"]
        proposer_vp_pct = (proposer_vp / total_vp * 100) if total_vp > 0 else 0

        top_voter = top_voter_vp = None
        if votes:
            top_vote = max(votes, key=lambda x:x["vp"])
            top_voter, top_voter_vp = top_vote["voter"], top_vote["vp"]

        outcome = "Unknown"
        if p["scores"] and len(p["scores"]) > 1:
            top_choice_index = p["scores"].index(max(p["scores"]))
            outcome = "Passed" if top_choice_index == 0 else "Failed"

        proposer_aligned = None
        if proposer_choice is not None and outcome != "Unknown":
            proposer_aligned = (proposer_choice == top_choice_index + 1)

        snapshot_results.append({
            "DAO": dao,
            "Proposal ID": p["id"],
            "Title": title,
            "Body": body[:200],
            "Proposer": proposer,
            "Proposer VP": proposer_vp,
            "Proposer VP %": round(proposer_vp_pct,2),
            "Proposer Aligned": proposer_aligned,
            "Start (Unix)": p["start"],
            "End (Unix)": p["end"],
            "State": p["state"],
            "Choices": p["choices"],
            "Total Votes (ballots)": len(votes),
            "Total VP (score sum)": total_vp,
            "Top Voter": top_voter,
            "Top Voter VP": top_voter_vp,
            "Outcome": outcome
        })
    time.sleep(0.3)
'''
def filter_before(proposals):
  accepted_proposals = []
  keywords = [
    "replace","remove","oust","vote out","impeach","recall","fire",
    "elect","appoint","nominate","re-elect","install","sack",
    "amend","update charter","bylaw","constitution","modify rules",
    "change rules","adjust policy","alter policy","policy change",
    "revise terms","ratify","motion to change",
    "withdraw funds","transfer funds","fund allocation","reallocate budget",
    "budget change","treasury control","redirect funds","cut funding","fund freeze",
    "investigate","audit","review","accountability","watchdog","reporting",
    "oversight","remove steward","replace facilitator",
    "quorum","voting threshold","change voting power","delegate removal",
    "remove delegation","change voting rights","adjust voting weight",
    "pivot","terminate project","halt","pause","sunset","wind down",
    "stop funding","cancel","scrap","shut down","cease operations",
    "lawsuit","litigation","legal","ban","blacklist","suspend","penalize",
    "take control","seize","reclaim","redeploy","change leadership",
    "power shift","redistribute power"
  ]
  classifer = pipeline("zero-shot-classification", model="facebook/bart-large-mnli")
  for proposal in proposals:
      if any(keyword in proposal["body"] for keyword in keywords):
        labels = ["activist", "non-activist"]
        hypothesis = "This proposal is {} because it involves significant activism, such as changing governance structure, reallocating treasury funds, accquiring or merging with another DAO, enforcing the use of an external product, or influencing strategic control"
        #change the hypothesis, this shi sort of ahh
        result = classifier(proposal["body"], candidate_labels=labels, hypothesis_template=hypothesis)
        if result >= 0.6:
          accepted_proposals.append(proposal)

  return accepted_proposals
"""
def filter_after(proposals):
    content = proposals["body"]
    tokenizer = DistilBertTokenizer,from_pretrained("distilbert-base-uncased")
    encodings = tokenizer(content, truncation=True, padding=True, max_length=256, return_tensors="pt")
    labels = [1, 0] #1 is activist, 0 is not activist
    ["labels"] = torch.tensor(labels)
    model = DistilBertForSequenceClassification.from_pretrained("distilbert-base-uncased", num_labels=2)
    ouputs = model(**encodings)

def main():
  proposals = deep_scraper()
  accepted_proposals = filter_before(proposals)
  """

if __name__ == "__main__":
  main()
