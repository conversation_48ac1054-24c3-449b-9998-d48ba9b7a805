<!DOCTYPE html><html><head><meta charSet="utf-8"/><meta content="width=device-width, initial-scale=1.0" name="viewport"/><title>Tally | Arbitrum | [CONSTITUTIONAL] Remove Cost Cap, Update Executors, Disable Legacy USDT Bridge</title><meta content="Tally" property="og:site_name"/><meta content="website" property="og:type"/><meta content="https://static.tally.xyz/f9b30a1e-faa0-4d0e-bf56-efd5939265cc_original.png" property="og:image"/><meta content="https://static.tally.xyz/f9b30a1e-faa0-4d0e-bf56-efd5939265cc_original.png" name="twitter:image"/><meta content="@tallyxyz" name="twitter:site"/><meta content="Tally | Arbitrum | [CONSTITUTIONAL] Remove Cost Cap, Update Executors, Disable Legacy USDT Bridge" property="og:title"/><meta content="[CONSTITUTIONAL] Remove Cost Cap, Update Executors, Disable Legacy USDT Bridge" property="og:description"/><meta content="vNext" name="fc:frame"/><meta content="https://frames.tally.xyz/api/proposal/image/frame-1?proposalId=51852039695020109312343918128899814224888993575448130385109956762385891284115&amp;governorSlug=arbitrum" name="fc:frame:image"/><meta content="1.91:1" name="fc:frame:image:aspect_ratio"/><meta content="Vote" name="fc:frame:button:1"/><meta content="link" name="fc:frame:button:1:action"/><meta content="https://www.tally.xyz//gov/arbitrum/proposal/51852039695020109312343918128899814224888993575448130385109956762385891284115?govId=eip155:42161:******************************************" name="fc:frame:button:1:target"/><meta name="next-head-count" content="10"/><link rel="stylesheet" data-href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;family=DM+Sans:wght@400;500;700&amp;family=Source+Code+Pro:wght@400;500;600;700&amp;display=swap&amp;family=Plus+Jakarta+Sans:wght@800&amp;display=swap"/><link rel="preload" href="/_next/static/css/9ffde84db4fbbec3.css" as="style"/><link rel="stylesheet" href="/_next/static/css/9ffde84db4fbbec3.css" data-n-g=""/><link rel="preload" href="/_next/static/css/dc21be9ffbde9886.css" as="style"/><link rel="stylesheet" href="/_next/static/css/dc21be9ffbde9886.css" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="/_next/static/chunks/polyfills-78c92fac7aa8fdd8.js"></script><script src="/_next/static/chunks/webpack-742a56027d11c9f1.js" defer=""></script><script src="/_next/static/chunks/framework-abfeed0cde68c3d3.js" defer=""></script><script src="/_next/static/chunks/main-818a15c9f3571f4c.js" defer=""></script><script src="/_next/static/chunks/pages/_app-a7e80dfec29f8b43.js" defer=""></script><script src="/_next/static/chunks/e5caab33-50a405129ea060d6.js" defer=""></script><script src="/_next/static/chunks/8498-bb57b50da453bb01.js" defer=""></script><script src="/_next/static/chunks/4393-27b1cc19748877f1.js" defer=""></script><script src="/_next/static/chunks/6029-0b50b893a054709a.js" defer=""></script><script src="/_next/static/chunks/3112-4d6315d690d4fe26.js" defer=""></script><script src="/_next/static/chunks/4719-edcd95e50d4f897d.js" defer=""></script><script src="/_next/static/chunks/9778-d8ae065386f784a9.js" defer=""></script><script src="/_next/static/chunks/4541-cc5094aa3f6a9a8d.js" defer=""></script><script src="/_next/static/chunks/759-2fd4ae85ed00770a.js" defer=""></script><script src="/_next/static/chunks/5530-19fdaaf7a4b6c91f.js" defer=""></script><script src="/_next/static/chunks/8792-13b1d8e3f6734653.js" defer=""></script><script src="/_next/static/chunks/7751-7648b937851ef48c.js" defer=""></script><script src="/_next/static/chunks/7229-caedd2c9e88e969e.js" defer=""></script><script src="/_next/static/chunks/6063-959d4976b491e40c.js" defer=""></script><script src="/_next/static/chunks/6186-4ad79399726a60d6.js" defer=""></script><script src="/_next/static/chunks/1544-216b2457d3ec0472.js" defer=""></script><script src="/_next/static/chunks/7897-c947227702f48005.js" defer=""></script><script src="/_next/static/chunks/7356-8538404c3d00ee76.js" defer=""></script><script src="/_next/static/chunks/5583-fc9183843fc41ebb.js" defer=""></script><script src="/_next/static/chunks/1359-12bfa3617a4a62ea.js" defer=""></script><script src="/_next/static/chunks/1128-3da942d998752574.js" defer=""></script><script src="/_next/static/chunks/pages/gov/%5BgovernanceId%5D/proposal/%5BproposalId%5D-4bf88fb7a3715a67.js" defer=""></script><script src="/_next/static/ea2vspBnjsMfoIY-HtAfV/_buildManifest.js" defer=""></script><script src="/_next/static/ea2vspBnjsMfoIY-HtAfV/_ssgManifest.js" defer=""></script><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=DM+Sans:wght@400;500;700&family=Source+Code+Pro:wght@400;500;600;700&display=swap&family=Plus+Jakarta+Sans:wght@800&display=swap"/></head><body><div id="__next"><style data-emotion="css-global 1up5q03">:host,:root,[data-theme]{--chakra-ring-inset:var(--chakra-empty,/*!*/ /*!*/);--chakra-ring-offset-width:0px;--chakra-ring-offset-color:#fff;--chakra-ring-color:rgba(66, 153, 225, 0.6);--chakra-ring-offset-shadow:0 0 #0000;--chakra-ring-shadow:0 0 #0000;--chakra-space-x-reverse:0;--chakra-space-y-reverse:0;--chakra-colors-transparent:transparent;--chakra-colors-current:currentColor;--chakra-colors-black:#000000;--chakra-colors-white:#FFFFFF;--chakra-colors-whiteAlpha-50:rgba(255, 255, 255, 0.04);--chakra-colors-whiteAlpha-100:rgba(255, 255, 255, 0.06);--chakra-colors-whiteAlpha-200:rgba(255, 255, 255, 0.08);--chakra-colors-whiteAlpha-300:rgba(255, 255, 255, 0.16);--chakra-colors-whiteAlpha-400:rgba(255, 255, 255, 0.24);--chakra-colors-whiteAlpha-500:rgba(255, 255, 255, 0.36);--chakra-colors-whiteAlpha-600:rgba(255, 255, 255, 0.48);--chakra-colors-whiteAlpha-700:rgba(255, 255, 255, 0.64);--chakra-colors-whiteAlpha-800:rgba(255, 255, 255, 0.80);--chakra-colors-whiteAlpha-900:rgba(255, 255, 255, 0.92);--chakra-colors-blackAlpha-50:rgba(0, 0, 0, 0.04);--chakra-colors-blackAlpha-100:rgba(0, 0, 0, 0.06);--chakra-colors-blackAlpha-200:rgba(0, 0, 0, 0.08);--chakra-colors-blackAlpha-300:rgba(0, 0, 0, 0.16);--chakra-colors-blackAlpha-400:rgba(0, 0, 0, 0.24);--chakra-colors-blackAlpha-500:rgba(0, 0, 0, 0.36);--chakra-colors-blackAlpha-600:rgba(0, 0, 0, 0.48);--chakra-colors-blackAlpha-700:rgba(0, 0, 0, 0.64);--chakra-colors-blackAlpha-800:rgba(0, 0, 0, 0.80);--chakra-colors-blackAlpha-900:rgba(0, 0, 0, 0.92);--chakra-colors-gray-50:#FCFCFD;--chakra-colors-gray-100:#F2F4F7;--chakra-colors-gray-200:#EAECF0;--chakra-colors-gray-300:#D0D5DD;--chakra-colors-gray-400:#98A2B3;--chakra-colors-gray-500:#667085;--chakra-colors-gray-600:#475467;--chakra-colors-gray-700:#344054;--chakra-colors-gray-800:#1D2939;--chakra-colors-gray-900:#101828;--chakra-colors-gray-bg:#fafafa;--chakra-colors-red-50:#FFF0F0;--chakra-colors-red-100:#FFE6E7;--chakra-colors-red-200:#FFBDC2;--chakra-colors-red-300:#FF94A0;--chakra-colors-red-400:#FF6B81;--chakra-colors-red-500:#F44061;--chakra-colors-red-600:#CF2B4F;--chakra-colors-red-700:#A81B3E;--chakra-colors-red-800:#820E2F;--chakra-colors-red-900:#5C0923;--chakra-colors-orange-50:#FFFAF0;--chakra-colors-orange-100:#FEEBC8;--chakra-colors-orange-200:#FBD38D;--chakra-colors-orange-300:#F6AD55;--chakra-colors-orange-400:#ED8936;--chakra-colors-orange-500:#DD6B20;--chakra-colors-orange-600:#C05621;--chakra-colors-orange-700:#9C4221;--chakra-colors-orange-800:#7B341E;--chakra-colors-orange-900:#652B19;--chakra-colors-yellow-50:#FFFFF0;--chakra-colors-yellow-100:#FEFCBF;--chakra-colors-yellow-200:#FAF089;--chakra-colors-yellow-300:#F6E05E;--chakra-colors-yellow-400:#ECC94B;--chakra-colors-yellow-500:#D69E2E;--chakra-colors-yellow-600:#B7791F;--chakra-colors-yellow-700:#975A16;--chakra-colors-yellow-800:#744210;--chakra-colors-yellow-900:#5F370E;--chakra-colors-green-50:#F0FFF9;--chakra-colors-green-100:#CFFCEB;--chakra-colors-green-200:#9EF0D3;--chakra-colors-green-300:#71E3BF;--chakra-colors-green-400:#49D6AE;--chakra-colors-green-500:#25C9A1;--chakra-colors-green-600:#15A384;--chakra-colors-green-700:#0A7D68;--chakra-colors-green-800:#03574A;--chakra-colors-green-900:#01302B;--chakra-colors-teal-50:#D9FFFB;--chakra-colors-teal-100:#A3FFE8;--chakra-colors-teal-200:#7AFFE2;--chakra-colors-teal-300:#52FFDF;--chakra-colors-teal-400:#27F2D4;--chakra-colors-teal-500:#00E6CD;--chakra-colors-teal-600:#00BFAF;--chakra-colors-teal-700:#009991;--chakra-colors-teal-800:#007371;--chakra-colors-teal-900:#004B4D;--chakra-colors-blue-50:#ebf8ff;--chakra-colors-blue-100:#bee3f8;--chakra-colors-blue-200:#90cdf4;--chakra-colors-blue-300:#63b3ed;--chakra-colors-blue-400:#4299e1;--chakra-colors-blue-500:#3182ce;--chakra-colors-blue-600:#2b6cb0;--chakra-colors-blue-700:#2c5282;--chakra-colors-blue-800:#2a4365;--chakra-colors-blue-900:#1A365D;--chakra-colors-cyan-50:#EDFDFD;--chakra-colors-cyan-100:#C4F1F9;--chakra-colors-cyan-200:#9DECF9;--chakra-colors-cyan-300:#76E4F7;--chakra-colors-cyan-400:#0BC5EA;--chakra-colors-cyan-500:#00B5D8;--chakra-colors-cyan-600:#00A3C4;--chakra-colors-cyan-700:#0987A0;--chakra-colors-cyan-800:#086F83;--chakra-colors-cyan-900:#065666;--chakra-colors-purple-50:#F4F0FF;--chakra-colors-purple-100:#EBE5FF;--chakra-colors-purple-200:#E0D6FF;--chakra-colors-purple-300:#BEADFF;--chakra-colors-purple-400:#9985FF;--chakra-colors-purple-500:#725BFF;--chakra-colors-purple-600:#5243D9;--chakra-colors-purple-700:#372EB3;--chakra-colors-purple-800:#141466;--chakra-colors-purple-900:#0E103C;--chakra-colors-pink-50:#FFF5F7;--chakra-colors-pink-100:#FED7E2;--chakra-colors-pink-200:#FBB6CE;--chakra-colors-pink-300:#F687B3;--chakra-colors-pink-400:#ED64A6;--chakra-colors-pink-500:#D53F8C;--chakra-colors-pink-600:#B83280;--chakra-colors-pink-700:#97266D;--chakra-colors-pink-800:#702459;--chakra-colors-pink-900:#521B41;--chakra-colors-linkedin-50:#E8F4F9;--chakra-colors-linkedin-100:#CFEDFB;--chakra-colors-linkedin-200:#9BDAF3;--chakra-colors-linkedin-300:#68C7EC;--chakra-colors-linkedin-400:#34B3E4;--chakra-colors-linkedin-500:#00A0DC;--chakra-colors-linkedin-600:#008CC9;--chakra-colors-linkedin-700:#0077B5;--chakra-colors-linkedin-800:#005E93;--chakra-colors-linkedin-900:#004471;--chakra-colors-facebook-50:#E8F4F9;--chakra-colors-facebook-100:#D9DEE9;--chakra-colors-facebook-200:#B7C2DA;--chakra-colors-facebook-300:#6482C0;--chakra-colors-facebook-400:#4267B2;--chakra-colors-facebook-500:#385898;--chakra-colors-facebook-600:#314E89;--chakra-colors-facebook-700:#29487D;--chakra-colors-facebook-800:#223B67;--chakra-colors-facebook-900:#1E355B;--chakra-colors-messenger-50:#D0E6FF;--chakra-colors-messenger-100:#B9DAFF;--chakra-colors-messenger-200:#A2CDFF;--chakra-colors-messenger-300:#7AB8FF;--chakra-colors-messenger-400:#2E90FF;--chakra-colors-messenger-500:#0078FF;--chakra-colors-messenger-600:#0063D1;--chakra-colors-messenger-700:#0052AC;--chakra-colors-messenger-800:#003C7E;--chakra-colors-messenger-900:#002C5C;--chakra-colors-whatsapp-50:#dffeec;--chakra-colors-whatsapp-100:#b9f5d0;--chakra-colors-whatsapp-200:#90edb3;--chakra-colors-whatsapp-300:#65e495;--chakra-colors-whatsapp-400:#3cdd78;--chakra-colors-whatsapp-500:#22c35e;--chakra-colors-whatsapp-600:#179848;--chakra-colors-whatsapp-700:#0c6c33;--chakra-colors-whatsapp-800:#01421c;--chakra-colors-whatsapp-900:#001803;--chakra-colors-twitter-50:#E5F4FD;--chakra-colors-twitter-100:#C8E9FB;--chakra-colors-twitter-200:#A8DCFA;--chakra-colors-twitter-300:#83CDF7;--chakra-colors-twitter-400:#57BBF5;--chakra-colors-twitter-500:#1DA1F2;--chakra-colors-twitter-600:#1A94DA;--chakra-colors-twitter-700:#1681BF;--chakra-colors-twitter-800:#136B9E;--chakra-colors-twitter-900:#0D4D71;--chakra-colors-telegram-50:#E3F2F9;--chakra-colors-telegram-100:#C5E4F3;--chakra-colors-telegram-200:#A2D4EC;--chakra-colors-telegram-300:#7AC1E4;--chakra-colors-telegram-400:#47A9DA;--chakra-colors-telegram-500:#0088CC;--chakra-colors-telegram-600:#007AB8;--chakra-colors-telegram-700:#006BA1;--chakra-colors-telegram-800:#005885;--chakra-colors-telegram-900:#003F5E;--chakra-colors-primary-50:hsla(256, 100%, 98%, 1);--chakra-colors-primary-100:hsla(255, 100%, 95%, 1);--chakra-colors-primary-200:hsla(255, 100%, 92%, 1);--chakra-colors-primary-300:hsla(252, 100%, 84%, 1);--chakra-colors-primary-400:hsla(250, 100%, 76%, 1);--chakra-colors-primary-500:hsla(248, 100%, 68%, 1);--chakra-colors-primary-600:hsla(246, 66%, 56%, 1);--chakra-colors-primary-700:hsla(244, 59%, 44%, 1);--chakra-colors-primary-800:hsla(242, 66%, 33%, 1);--chakra-colors-primary-900:hsla(240, 67%, 24%, 1);--chakra-colors-success-50:hsla(138, 76%, 97%, 1);--chakra-colors-success-100:hsla(141, 84%, 93%, 1);--chakra-colors-success-200:hsla(141, 79%, 85%, 1);--chakra-colors-success-300:hsla(142, 77%, 73%, 1);--chakra-colors-success-400:hsla(142, 69%, 58%, 1);--chakra-colors-success-500:hsla(142, 71%, 45%, 1);--chakra-colors-success-600:hsla(142, 76%, 36%, 1);--chakra-colors-success-700:hsla(142, 72%, 29%, 1);--chakra-colors-success-800:hsla(143, 64%, 24%, 1);--chakra-colors-success-900:hsla(144, 61%, 20%, 1);--chakra-colors-warning-50:hsla(48, 100%, 96%, 1);--chakra-colors-warning-100:hsla(48, 96%, 89%, 1);--chakra-colors-warning-200:hsla(48, 97%, 77%, 1);--chakra-colors-warning-300:hsla(46, 97%, 65%, 1);--chakra-colors-warning-400:hsla(43, 96%, 56%, 1);--chakra-colors-warning-500:hsla(38, 92%, 50%, 1);--chakra-colors-warning-600:hsla(32, 95%, 44%, 1);--chakra-colors-warning-700:hsla(26, 90%, 37%, 1);--chakra-colors-warning-800:hsla(23, 82%, 31%, 1);--chakra-colors-warning-900:hsla(22, 78%, 26%, 1);--chakra-colors-destructive-50:hsla(0, 86%, 97%, 1);--chakra-colors-destructive-100:hsla(0, 93%, 94%, 1);--chakra-colors-destructive-200:hsla(0, 96%, 89%, 1);--chakra-colors-destructive-300:hsla(0, 94%, 82%, 1);--chakra-colors-destructive-400:hsla(0, 91%, 71%, 1);--chakra-colors-destructive-500:hsla(0, 84%, 60%, 1);--chakra-colors-destructive-600:hsla(0, 72%, 51%, 1);--chakra-colors-destructive-700:hsla(0, 74%, 42%, 1);--chakra-colors-destructive-800:hsla(0, 70%, 35%, 1);--chakra-colors-destructive-900:hsla(0, 63%, 31%, 1);--chakra-colors-neutral-50:hsla(210, 20%, 98%, 1);--chakra-colors-neutral-100:hsla(220, 14%, 96%, 1);--chakra-colors-neutral-200:hsla(220, 13%, 91%, 1);--chakra-colors-neutral-300:hsla(216, 12%, 84%, 1);--chakra-colors-neutral-400:hsla(218, 11%, 65%, 1);--chakra-colors-neutral-500:hsla(220, 9%, 46%, 1);--chakra-colors-neutral-600:hsla(215, 14%, 34%, 1);--chakra-colors-neutral-700:hsla(217, 19%, 27%, 1);--chakra-colors-neutral-800:hsla(215, 28%, 17%, 1);--chakra-colors-neutral-900:hsla(221, 39%, 11%, 1);--chakra-colors-external-twitter:#1DA1F2;--chakra-colors-external-twitterHover:#158BD4;--chakra-borders-none:0;--chakra-borders-1px:1px solid;--chakra-borders-2px:2px solid;--chakra-borders-4px:4px solid;--chakra-borders-8px:8px solid;--chakra-borders-white-1:1px solid #FFFFFF;--chakra-borders-white-3:3px solid #FFFFFF;--chakra-borders-gray-200:1px solid #E2E8F0;--chakra-borders-gray-300:1px solid #CBD5E0;--chakra-borders-gray-700:1px solid #2D3748;--chakra-borders-gray-light:1px solid #EDF2F7;--chakra-borders-gray-dark:1px solid #E2E8F0;--chakra-borders-gray-darker:1px solid #444D56;--chakra-borders-green-500:1px solid #25C9A1;--chakra-borders-red-500:1px solid #F44061;--chakra-borders-red-bold:2px solid #F44061;--chakra-borders-blue-selected:1px solid #3182CE;--chakra-borders-blue-selected2:2px solid #3182CE;--chakra-borders-purple-500:1px solid #725BFF;--chakra-borders-transparent-1:1px solid transparent;--chakra-borders-transparent-4:4px solid transparent;--chakra-fonts-heading:DM Sans,Inter,sans-serif;--chakra-fonts-body:DM Sans,Inter,sans-serif;--chakra-fonts-mono:Source Code Pro,Inter,sans-serif;--chakra-fontSizes-3xs:0.45rem;--chakra-fontSizes-2xs:0.625rem;--chakra-fontSizes-xs:0.75rem;--chakra-fontSizes-sm:0.875rem;--chakra-fontSizes-md:1rem;--chakra-fontSizes-lg:1.125rem;--chakra-fontSizes-xl:1.25rem;--chakra-fontSizes-2xl:1.5rem;--chakra-fontSizes-3xl:1.875rem;--chakra-fontSizes-4xl:2.25rem;--chakra-fontSizes-5xl:3rem;--chakra-fontSizes-6xl:3.75rem;--chakra-fontSizes-7xl:4.5rem;--chakra-fontSizes-8xl:6rem;--chakra-fontSizes-9xl:8rem;--chakra-fontWeights-hairline:100;--chakra-fontWeights-thin:200;--chakra-fontWeights-light:300;--chakra-fontWeights-normal:400;--chakra-fontWeights-medium:500;--chakra-fontWeights-semibold:600;--chakra-fontWeights-bold:700;--chakra-fontWeights-extrabold:800;--chakra-fontWeights-black:900;--chakra-letterSpacings-tighter:-0.05em;--chakra-letterSpacings-tight:-0.025em;--chakra-letterSpacings-normal:0;--chakra-letterSpacings-wide:0.025em;--chakra-letterSpacings-wider:0.05em;--chakra-letterSpacings-widest:0.1em;--chakra-lineHeights-3:.75rem;--chakra-lineHeights-4:1rem;--chakra-lineHeights-5:1.25rem;--chakra-lineHeights-6:1.5rem;--chakra-lineHeights-7:1.75rem;--chakra-lineHeights-8:2rem;--chakra-lineHeights-9:2.25rem;--chakra-lineHeights-10:2.5rem;--chakra-lineHeights-normal:normal;--chakra-lineHeights-none:1;--chakra-lineHeights-shorter:1.25;--chakra-lineHeights-short:1.375;--chakra-lineHeights-base:1.5;--chakra-lineHeights-tall:1.625;--chakra-lineHeights-taller:2;--chakra-radii-none:0;--chakra-radii-sm:0.125rem;--chakra-radii-base:0.25rem;--chakra-radii-md:0.375rem;--chakra-radii-lg:0.5rem;--chakra-radii-xl:0.75rem;--chakra-radii-2xl:1rem;--chakra-radii-3xl:1.5rem;--chakra-radii-full:9999px;--chakra-space-1:0.25rem;--chakra-space-2:0.5rem;--chakra-space-3:0.75rem;--chakra-space-4:1rem;--chakra-space-5:1.25rem;--chakra-space-6:1.5rem;--chakra-space-7:1.75rem;--chakra-space-8:2rem;--chakra-space-9:2.25rem;--chakra-space-10:2.5rem;--chakra-space-11:2.75rem;--chakra-space-12:3rem;--chakra-space-13:3.25rem;--chakra-space-14:3.5rem;--chakra-space-15:3.75rem;--chakra-space-16:4rem;--chakra-space-17:4.25rem;--chakra-space-18:4.5rem;--chakra-space-19:4.75rem;--chakra-space-20:5rem;--chakra-space-21:5.25rem;--chakra-space-22:5.5rem;--chakra-space-24:6rem;--chakra-space-28:7rem;--chakra-space-32:8rem;--chakra-space-36:9rem;--chakra-space-40:10rem;--chakra-space-44:11rem;--chakra-space-48:12rem;--chakra-space-52:13rem;--chakra-space-56:14rem;--chakra-space-60:15rem;--chakra-space-64:16rem;--chakra-space-72:18rem;--chakra-space-80:20rem;--chakra-space-88:22rem;--chakra-space-96:24rem;--chakra-space-px:1px;--chakra-space-0-5:0.125rem;--chakra-space-1-5:0.375rem;--chakra-space-2-5:0.625rem;--chakra-space-3-5:0.875rem;--chakra-space-containerGap-base:3;--chakra-space-containerGap-lg:4;--chakra-space-containerPadding-base:3;--chakra-space-containerPadding-lg:4;--chakra-shadows-xs:0 0 0 1px rgba(0, 0, 0, 0.05);--chakra-shadows-sm:0 1px 2px 0 rgba(0, 0, 0, 0.05);--chakra-shadows-base:0 1px 3px 0 rgba(0, 0, 0, 0.1),0 1px 2px 0 rgba(0, 0, 0, 0.06);--chakra-shadows-md:0 4px 6px -1px rgba(0, 0, 0, 0.1),0 2px 4px -1px rgba(0, 0, 0, 0.06);--chakra-shadows-lg:0 10px 15px -3px rgba(0, 0, 0, 0.1),0 4px 6px -2px rgba(0, 0, 0, 0.05);--chakra-shadows-xl:0 20px 25px -5px rgba(0, 0, 0, 0.1),0 10px 10px -5px rgba(0, 0, 0, 0.04);--chakra-shadows-2xl:0 25px 50px -12px rgba(0, 0, 0, 0.25);--chakra-shadows-outline:0 0 0 1px rgba(45, 55, 72, 0.6);--chakra-shadows-inner:inset 0 2px 4px 0 rgba(0,0,0,0.06);--chakra-shadows-none:none;--chakra-shadows-dark-lg:rgba(0, 0, 0, 0.1) 0px 0px 0px 1px,rgba(0, 0, 0, 0.2) 0px 5px 10px,rgba(0, 0, 0, 0.4) 0px 15px 40px;--chakra-shadows-purple-light:inset 2px -2px #EBE5FF,inset 2px 2px #EBE5FF,inset -2px 2px #EBE5FF,inset -2px -2px #EBE5FF;--chakra-shadows-gray-1:0px 2px 8px rgba(203, 213, 224, 0.32);--chakra-shadows-gray-2:0px 4px 12px rgba(203, 213, 224, 0.24);--chakra-shadows-gray-3:0px 12px 16px rgba(203, 213, 224, 0.24);--chakra-shadows-elevation-1:0px 1px 5px rgba(0, 0, 0, 0.15);--chakra-shadows-elevation-2:0px 4px 12px rgba(113, 128, 150, 0.24);--chakra-shadows-blue-selected:0 0 0 1px #3182CE;--chakra-sizes-1:0.25rem;--chakra-sizes-2:0.5rem;--chakra-sizes-3:0.75rem;--chakra-sizes-4:1rem;--chakra-sizes-5:1.25rem;--chakra-sizes-6:1.5rem;--chakra-sizes-7:1.75rem;--chakra-sizes-8:2rem;--chakra-sizes-9:2.25rem;--chakra-sizes-10:2.5rem;--chakra-sizes-12:3rem;--chakra-sizes-14:3.5rem;--chakra-sizes-16:4rem;--chakra-sizes-20:5rem;--chakra-sizes-24:6rem;--chakra-sizes-28:7rem;--chakra-sizes-32:8rem;--chakra-sizes-36:9rem;--chakra-sizes-40:10rem;--chakra-sizes-44:11rem;--chakra-sizes-48:12rem;--chakra-sizes-52:13rem;--chakra-sizes-56:14rem;--chakra-sizes-60:15rem;--chakra-sizes-64:16rem;--chakra-sizes-72:18rem;--chakra-sizes-80:20rem;--chakra-sizes-96:24rem;--chakra-sizes-px:1px;--chakra-sizes-0-5:0.125rem;--chakra-sizes-1-5:0.375rem;--chakra-sizes-2-5:0.625rem;--chakra-sizes-3-5:0.875rem;--chakra-sizes-max:max-content;--chakra-sizes-min:min-content;--chakra-sizes-full:100%;--chakra-sizes-3xs:14rem;--chakra-sizes-2xs:16rem;--chakra-sizes-xs:20rem;--chakra-sizes-sm:24rem;--chakra-sizes-md:28rem;--chakra-sizes-lg:32rem;--chakra-sizes-xl:36rem;--chakra-sizes-2xl:42rem;--chakra-sizes-3xl:48rem;--chakra-sizes-4xl:56rem;--chakra-sizes-5xl:64rem;--chakra-sizes-6xl:72rem;--chakra-sizes-7xl:80rem;--chakra-sizes-8xl:90rem;--chakra-sizes-prose:60ch;--chakra-sizes-container-sm:640px;--chakra-sizes-container-md:768px;--chakra-sizes-container-lg:1024px;--chakra-sizes-container-xl:1280px;--chakra-zIndices-hide:-1;--chakra-zIndices-auto:auto;--chakra-zIndices-base:0;--chakra-zIndices-docked:10;--chakra-zIndices-dropdown:1000;--chakra-zIndices-sticky:1100;--chakra-zIndices-banner:1200;--chakra-zIndices-overlay:1300;--chakra-zIndices-modal:1400;--chakra-zIndices-popover:1500;--chakra-zIndices-skipLink:1600;--chakra-zIndices-toast:1700;--chakra-zIndices-tooltip:1800;--chakra-transition-property-common:background-color,border-color,color,fill,stroke,opacity,box-shadow,transform;--chakra-transition-property-colors:background-color,border-color,color,fill,stroke;--chakra-transition-property-dimensions:width,height;--chakra-transition-property-position:left,right,top,bottom;--chakra-transition-property-background:background-color,background-image,background-position;--chakra-transition-easing-ease-in:cubic-bezier(0.4, 0, 1, 1);--chakra-transition-easing-ease-out:cubic-bezier(0, 0, 0.2, 1);--chakra-transition-easing-ease-in-out:cubic-bezier(0.4, 0, 0.2, 1);--chakra-transition-duration-ultra-fast:50ms;--chakra-transition-duration-faster:100ms;--chakra-transition-duration-fast:150ms;--chakra-transition-duration-normal:200ms;--chakra-transition-duration-slow:300ms;--chakra-transition-duration-slower:400ms;--chakra-transition-duration-ultra-slow:500ms;--chakra-blur-none:0;--chakra-blur-sm:4px;--chakra-blur-base:8px;--chakra-blur-md:12px;--chakra-blur-lg:16px;--chakra-blur-xl:24px;--chakra-blur-2xl:40px;--chakra-blur-3xl:64px;--chakra-breakpoints-base:0em;--chakra-breakpoints-sm:30em;--chakra-breakpoints-md:48em;--chakra-breakpoints-lg:62em;--chakra-breakpoints-xl:80em;--chakra-breakpoints-2xl:96em;}.chakra-ui-light :host:not([data-theme]),.chakra-ui-light :root:not([data-theme]),.chakra-ui-light [data-theme]:not([data-theme]),[data-theme=light] :host:not([data-theme]),[data-theme=light] :root:not([data-theme]),[data-theme=light] [data-theme]:not([data-theme]),:host[data-theme=light],:root[data-theme=light],[data-theme][data-theme=light]{--chakra-colors-chakra-body-text:var(--chakra-colors-gray-800);--chakra-colors-chakra-body-bg:var(--chakra-colors-white);--chakra-colors-chakra-border-color:var(--chakra-colors-gray-200);--chakra-colors-chakra-inverse-text:var(--chakra-colors-white);--chakra-colors-chakra-subtle-bg:var(--chakra-colors-gray-100);--chakra-colors-chakra-subtle-text:var(--chakra-colors-gray-600);--chakra-colors-chakra-placeholder-color:var(--chakra-colors-gray-500);}.chakra-ui-dark :host:not([data-theme]),.chakra-ui-dark :root:not([data-theme]),.chakra-ui-dark [data-theme]:not([data-theme]),[data-theme=dark] :host:not([data-theme]),[data-theme=dark] :root:not([data-theme]),[data-theme=dark] [data-theme]:not([data-theme]),:host[data-theme=dark],:root[data-theme=dark],[data-theme][data-theme=dark]{--chakra-colors-chakra-body-text:var(--chakra-colors-whiteAlpha-900);--chakra-colors-chakra-body-bg:var(--chakra-colors-gray-800);--chakra-colors-chakra-border-color:var(--chakra-colors-whiteAlpha-300);--chakra-colors-chakra-inverse-text:var(--chakra-colors-gray-800);--chakra-colors-chakra-subtle-bg:var(--chakra-colors-gray-700);--chakra-colors-chakra-subtle-text:var(--chakra-colors-gray-400);--chakra-colors-chakra-placeholder-color:var(--chakra-colors-whiteAlpha-400);}</style><style data-emotion="css-global fubdgu">html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:system-ui,sans-serif;-webkit-font-smoothing:antialiased;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale;touch-action:manipulation;}body{position:relative;min-height:100%;margin:0;font-feature-settings:"kern";}:where(*, *::before, *::after){border-width:0;border-style:solid;box-sizing:border-box;word-wrap:break-word;}main{display:block;}hr{border-top-width:1px;box-sizing:content-box;height:0;overflow:visible;}:where(pre, code, kbd,samp){font-family:SFMono-Regular,Menlo,Monaco,Consolas,monospace;font-size:1em;}a{background-color:transparent;color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit;}abbr[title]{border-bottom:none;-webkit-text-decoration:underline;text-decoration:underline;-webkit-text-decoration:underline dotted;-webkit-text-decoration:underline dotted;text-decoration:underline dotted;}:where(b, strong){font-weight:bold;}small{font-size:80%;}:where(sub,sup){font-size:75%;line-height:0;position:relative;vertical-align:baseline;}sub{bottom:-0.25em;}sup{top:-0.5em;}img{border-style:none;}:where(button, input, optgroup, select, textarea){font-family:inherit;font-size:100%;line-height:1.15;margin:0;}:where(button, input){overflow:visible;}:where(button, select){text-transform:none;}:where(
          button::-moz-focus-inner,
          [type="button"]::-moz-focus-inner,
          [type="reset"]::-moz-focus-inner,
          [type="submit"]::-moz-focus-inner
        ){border-style:none;padding:0;}fieldset{padding:0.35em 0.75em 0.625em;}legend{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal;}progress{vertical-align:baseline;}textarea{overflow:auto;}:where([type="checkbox"], [type="radio"]){box-sizing:border-box;padding:0;}input[type="number"]::-webkit-inner-spin-button,input[type="number"]::-webkit-outer-spin-button{-webkit-appearance:none!important;}input[type="number"]{-moz-appearance:textfield;}input[type="search"]{-webkit-appearance:textfield;outline-offset:-2px;}input[type="search"]::-webkit-search-decoration{-webkit-appearance:none!important;}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit;}details{display:block;}summary{display:-webkit-box;display:-webkit-list-item;display:-ms-list-itembox;display:list-item;}template{display:none;}[hidden]{display:none!important;}:where(
          blockquote,
          dl,
          dd,
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          hr,
          figure,
          p,
          pre
        ){margin:0;}button{background:transparent;padding:0;}fieldset{margin:0;padding:0;}:where(ol, ul){margin:0;padding:0;}textarea{resize:vertical;}:where(button, [role="button"]){cursor:pointer;}button::-moz-focus-inner{border:0!important;}table{border-collapse:collapse;}:where(h1, h2, h3, h4, h5, h6){font-size:inherit;font-weight:inherit;}:where(button, input, optgroup, select, textarea){padding:0;line-height:inherit;color:inherit;}:where(img, svg, video, canvas, audio, iframe, embed, object){display:block;}:where(img, video){max-width:100%;height:auto;}[data-js-focus-visible] :focus:not([data-focus-visible-added]):not(
          [data-focus-visible-disabled]
        ){outline:none;box-shadow:none;}select::-ms-expand{display:none;}:root,:host{--chakra-vh:100vh;}@supports (height: -webkit-fill-available){:root,:host{--chakra-vh:-webkit-fill-available;}}@supports (height: -moz-fill-available){:root,:host{--chakra-vh:-moz-fill-available;}}@supports (height: 100dvh){:root,:host{--chakra-vh:100dvh;}}</style><style data-emotion="css-global e28jx5">body{font-family:var(--chakra-fonts-body);color:var(--chakra-colors-chakra-body-text);background:var(--chakra-colors-chakra-body-bg);transition-property:background-color;transition-duration:var(--chakra-transition-duration-normal);line-height:var(--chakra-lineHeights-base);}*::-webkit-input-placeholder{color:var(--chakra-colors-chakra-placeholder-color);}*::-moz-placeholder{color:var(--chakra-colors-chakra-placeholder-color);}*:-ms-input-placeholder{color:var(--chakra-colors-chakra-placeholder-color);}*::placeholder{color:var(--chakra-colors-chakra-placeholder-color);}*,*::before,::after{border-color:var(--chakra-colors-chakra-border-color);}div [aria-hidden='true']{border:var(--chakra-borders-none);}dl{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;}</style><style data-emotion="css t5rg0x">.css-t5rg0x{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;background:var(--chakra-colors-gray-bg);min-height:100vh;width:var(--chakra-sizes-full);}</style><div class="main-layout css-t5rg0x"><style data-emotion="css agqapa">.css-agqapa{width:100%;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;max-width:var(--chakra-sizes-6xl);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);}@media screen and (min-width: 62em){.css-agqapa{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;}}</style><div class="chakra-container css-agqapa"><style data-emotion="css ummh7r">.css-ummh7r{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;margin-top:var(--chakra-space-3);}@media screen and (min-width: 62em){.css-ummh7r{display:none;margin-top:var(--chakra-space-4);}}</style><nav aria-label="breadcrumb" class="chakra-breadcrumb css-ummh7r"><style data-emotion="css 70qvj9">.css-70qvj9{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}</style><ol class="chakra-breadcrumb__list css-70qvj9"><style data-emotion="css g55rz7">.css-g55rz7{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;color:var(--chakra-colors-gray-400);}</style><li class="chakra-breadcrumb__list-item css-g55rz7"><style data-emotion="css 1sn59g1">.css-1sn59g1{transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-fast);transition-timing-function:var(--chakra-transition-easing-ease-out);outline:2px solid transparent;outline-offset:2px;-webkit-text-decoration:var(--breadcrumb-link-decor);text-decoration:var(--breadcrumb-link-decor);--breadcrumb-link-decor:none;color:var(--chakra-colors-gray-600);font-weight:var(--chakra-fontWeights-medium);}.css-1sn59g1:not([aria-current=page]){cursor:pointer;}.css-1sn59g1:not([aria-current=page]):hover,.css-1sn59g1:not([aria-current=page])[data-hover]{--breadcrumb-link-decor:underline;}.css-1sn59g1:not([aria-current=page]):focus-visible,.css-1sn59g1:not([aria-current=page])[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}</style><div class="chakra-breadcrumb__link css-1sn59g1"><style data-emotion="css nuz2iz">.css-nuz2iz{transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-fast);transition-timing-function:var(--chakra-transition-easing-ease-out);cursor:pointer;-webkit-text-decoration:none;text-decoration:none;outline:2px solid transparent;outline-offset:2px;color:inherit;font-family:DM Sans;}.css-nuz2iz:hover,.css-nuz2iz[data-hover]{-webkit-text-decoration:underline;text-decoration:underline;}.css-nuz2iz:focus-visible,.css-nuz2iz[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}</style><a class="chakra-link css-nuz2iz" href="/gov/arbitrum/proposals"><style data-emotion="css 1igwmid">.css-1igwmid{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;gap:0.5rem;}</style><div class="chakra-stack css-1igwmid"><style data-emotion="css fw0nz8">.css-fw0nz8{display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;fill:var(--chakra-colors-gray-600);height:var(--chakra-sizes-3);margin-left:var(--chakra-space-1);width:var(--chakra-sizes-3);}</style><style data-emotion="css 1bj5ure">.css-1bj5ure{width:1em;height:1em;display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;vertical-align:middle;display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;fill:var(--chakra-colors-gray-600);height:var(--chakra-sizes-3);margin-left:var(--chakra-space-1);width:var(--chakra-sizes-3);}</style><svg viewBox="0 0 448 512" focusable="false" class="chakra-icon chakra-icon css-1bj5ure"><path d="M448 256c0 8.8-7.4 16-16.6 16H54.11l140.7 149.3c6.157 6.531 5.655 16.66-1.118 22.59A17.316 17.316 0 01182.5 448c-4.505 0-9.009-1.75-12.28-5.25l-165.9-176c-5.752-6.094-5.752-15.41 0-21.5l165.9-176c6.19-6.562 16.69-7 23.45-1.094 6.773 5.938 7.275 16.06 1.118 22.59L54.11 240h377.3c9.19 0 16.59 7.2 16.59 16z"></path></svg><p class="chakra-text css-0">Proposals</p></div></a></div></li></ol></nav><style data-emotion="css 5nbgr1">.css-5nbgr1{display:none;margin-top:var(--chakra-space-4);}@media screen and (min-width: 62em){.css-5nbgr1{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}}</style><nav aria-label="breadcrumb" class="chakra-breadcrumb css-5nbgr1"><ol class="chakra-breadcrumb__list css-70qvj9"><li class="chakra-breadcrumb__list-item css-g55rz7"><style data-emotion="css 1mwwt6">.css-1mwwt6{transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-fast);transition-timing-function:var(--chakra-transition-easing-ease-out);outline:2px solid transparent;outline-offset:2px;-webkit-text-decoration:var(--breadcrumb-link-decor);text-decoration:var(--breadcrumb-link-decor);--breadcrumb-link-decor:none;color:var(--chakra-colors-gray-400);}.css-1mwwt6:not([aria-current=page]){cursor:pointer;}.css-1mwwt6:not([aria-current=page]):hover,.css-1mwwt6:not([aria-current=page])[data-hover]{--breadcrumb-link-decor:underline;}.css-1mwwt6:not([aria-current=page]):focus-visible,.css-1mwwt6:not([aria-current=page])[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}</style><div class="chakra-breadcrumb__link css-1mwwt6"><a class="chakra-link css-nuz2iz" href="/gov/arbitrum/proposals">Proposals</a></div><style data-emotion="css t4q1nq">.css-t4q1nq{-webkit-margin-start:0.5rem;margin-inline-start:0.5rem;-webkit-margin-end:0.5rem;margin-inline-end:0.5rem;}</style><span role="presentation" class="css-t4q1nq"><style data-emotion="css 119zpey">.css-119zpey{color:var(--chakra-colors-gray-300);}</style><p class="chakra-text css-119zpey">/</p></span></li><li class="chakra-breadcrumb__list-item css-g55rz7"><style data-emotion="css 1v5yhy4">.css-1v5yhy4{color:var(--chakra-colors-gray-600);font-weight:var(--chakra-fontWeights-bold);font-family:DM Sans;font-style:normal;font-size:16px;line-height:24px;}</style><p class="chakra-text css-1v5yhy4">Proposal</p></li></ol></nav></div><style data-emotion="css 4hh78x">.css-4hh78x{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex:1;-ms-flex:1;flex:1;width:var(--chakra-sizes-full);}</style><div class="css-4hh78x"></div><style data-emotion="css 1zdvg3">.css-1zdvg3{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:0px;background:var(--chakra-colors-white);border-top:var(--chakra-borders-gray-dark);margin-top:0px;-webkit-padding-start:var(--chakra-space-6);padding-inline-start:var(--chakra-space-6);-webkit-padding-end:var(--chakra-space-6);padding-inline-end:var(--chakra-space-6);padding-top:var(--chakra-space-8);padding-bottom:var(--chakra-space-8);width:var(--chakra-sizes-full);}@media screen and (min-width: 62em){.css-1zdvg3{-webkit-align-items:flex-start;-webkit-box-align:flex-start;-ms-flex-align:flex-start;align-items:flex-start;gap:var(--chakra-space-12);-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;margin-top:var(--chakra-space-20);-webkit-padding-start:var(--chakra-space-18);padding-inline-start:var(--chakra-space-18);-webkit-padding-end:var(--chakra-space-18);padding-inline-end:var(--chakra-space-18);padding-top:var(--chakra-space-10);padding-bottom:var(--chakra-space-10);}}</style><footer class="chakra-stack css-1zdvg3"><style data-emotion="css 11w9q8d">.css-11w9q8d{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;width:var(--chakra-sizes-full);}@media screen and (min-width: 62em){.css-11w9q8d{-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;-webkit-margin-start:var(--chakra-space-4);margin-inline-start:var(--chakra-space-4);-webkit-margin-end:var(--chakra-space-4);margin-inline-end:var(--chakra-space-4);width:auto;}}</style><div class="css-11w9q8d"><style data-emotion="css kv80fw">.css-kv80fw{margin-right:0px;}@media screen and (min-width: 62em){.css-kv80fw{margin-right:var(--chakra-space-36);}}</style><div class="css-kv80fw"><a class="chakra-link no-underline css-nuz2iz" href="/explore"><style data-emotion="css 1796id3">.css-1796id3{display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;height:var(--chakra-sizes-8);width:var(--chakra-sizes-20);}@media screen and (min-width: 62em){.css-1796id3{height:var(--chakra-sizes-9);width:var(--chakra-sizes-24);}}</style><style data-emotion="css ufwsys">.css-ufwsys{width:1em;height:1em;display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;vertical-align:middle;display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;height:var(--chakra-sizes-8);width:var(--chakra-sizes-20);}@media screen and (min-width: 62em){.css-ufwsys{height:var(--chakra-sizes-9);width:var(--chakra-sizes-24);}}</style><svg viewBox="0 0 706 160" focusable="false" class="chakra-icon chakra-icon css-ufwsys"><g clip-path="url(#clip0_3_570)"><path clip-rule="evenodd" d="M96.8981 89.6686V64.4458C96.8981 62.7933 96.0031 61.2671 94.5686 60.4523L14.4246 15.1934V10.5688C14.4246 8.81311 16.318 7.71147 17.8442 8.57212L102.108 56.172C103.554 56.9868 104.437 58.513 104.437 60.1654V90.0129C104.437 91.7686 102.544 92.8703 101.018 92.0096L96.8867 89.6801L96.8981 89.6686ZM75.6343 101.97L68.0835 97.7014V148.526L72.2146 150.855C73.7408 151.716 75.6343 150.614 75.6343 148.859V101.959V101.97Z" fill="#1C1C1C" fill-rule="evenodd"></path><path clip-rule="evenodd" d="M111.346 81.4062V56.1833C111.346 54.5309 110.451 53.0046 109.016 52.1899L28.8835 6.93097V2.29492C28.8835 0.539182 30.777 -0.562455 32.3032 0.298199L116.567 47.8981C118.013 48.7128 118.896 50.2391 118.896 51.8915V81.739C118.896 83.4947 117.003 84.5964 115.477 83.7357L111.346 81.4062ZM88.7965 109.223L82.531 105.711V146.609L86.5473 148.859C88.0736 149.719 89.967 148.606 89.967 146.862V111.219C89.967 110.393 89.5195 109.624 88.7965 109.223Z" fill="#1C1C1C" fill-rule="evenodd"></path><path d="M90.0244 68.2785V98.1259C90.0244 99.8817 88.1309 100.983 86.6047 100.123L62.9195 86.7424C62.1507 86.3063 61.2097 86.8571 61.2097 87.7407V156.983C61.2097 158.739 59.3163 159.841 57.79 158.98L31.1671 143.936C29.7212 143.121 28.8376 141.595 28.8376 139.942V68.8408C28.8376 68.0145 28.3901 67.2457 27.6671 66.8441L2.3295 52.5343C0.883604 51.7195 0 50.1933 0 48.5408V18.6934C0 16.9376 1.89344 15.836 3.41966 16.6966L87.6834 64.2965C89.1293 65.1113 90.0129 66.6375 90.0129 68.29L90.0244 68.2785Z" fill="#1C1C1C"></path><path d="M496.758 151.211C499.294 151.211 501.348 149.157 501.348 146.621V114.192C501.348 111.656 499.294 109.601 496.758 109.601H464.81C463.548 109.601 462.515 108.569 462.515 107.306V47.0262C462.515 44.4901 460.461 42.436 457.925 42.436H418.542C416.006 42.436 413.951 44.4901 413.951 47.0262V146.621C413.951 149.157 416.006 151.211 418.542 151.211H496.758Z" fill="#1C1C1C"></path><path d="M597.867 151.211C600.403 151.211 602.458 149.157 602.458 146.621V114.192C602.458 111.656 600.403 109.601 597.867 109.601H565.92C564.658 109.601 563.625 108.569 563.625 107.306V47.0262C563.625 44.4901 561.571 42.436 559.035 42.436H519.651C517.115 42.436 515.061 44.4901 515.061 47.0262V146.621C515.061 149.157 517.115 151.211 519.651 151.211H597.867Z" fill="#1C1C1C"></path><path d="M297.58 47.0145V79.4439C297.58 81.98 295.525 84.0341 292.989 84.0341H264.599C263.337 84.0341 262.304 85.0668 262.304 86.3291V146.609C262.304 149.145 260.25 151.199 257.714 151.199H218.331C215.795 151.199 213.74 149.145 213.74 146.609V86.3291C213.74 85.0668 212.708 84.0341 211.445 84.0341H183.055C180.519 84.0341 178.465 81.98 178.465 79.4439V47.0145C178.465 44.4784 180.519 42.4243 183.055 42.4243H292.978C295.514 42.4243 297.568 44.4784 297.568 47.0145H297.58Z" fill="#1C1C1C"></path><path d="M402.04 145.037L365.594 45.4423C364.929 43.6292 363.207 42.4243 361.28 42.4243H328.207C326.28 42.4243 324.558 43.6292 323.893 45.4423L287.447 145.037C286.345 148.032 288.572 151.199 291.762 151.199H330.285C332.339 151.199 334.14 149.834 334.703 147.86L337.239 138.886C337.514 137.899 338.421 137.211 339.442 137.211H350.034C351.066 137.211 351.962 137.888 352.237 138.886L354.773 147.86C355.335 149.834 357.137 151.199 359.191 151.199H397.714C400.904 151.199 403.119 148.032 402.029 145.037H402.04ZM352.203 109.28H337.296C335.701 109.28 334.588 107.696 335.139 106.193L342.598 85.8816C343.332 83.8734 346.166 83.8734 346.912 85.8816L354.371 106.193C354.922 107.696 353.809 109.28 352.214 109.28H352.203Z" fill="#1C1C1C"></path><path d="M701.41 42.436H663.954C662.049 42.436 660.339 43.6065 659.662 45.3852L648.566 74.4524C647.82 76.4261 645.031 76.4261 644.274 74.4524L633.177 45.3852C632.5 43.6065 630.79 42.436 628.886 42.436H591.43C587.895 42.436 585.692 46.2573 587.448 49.3213L621.518 108.431C621.92 109.131 622.127 109.923 622.127 110.726V146.621C622.127 149.157 624.181 151.211 626.717 151.211H666.1C668.636 151.211 670.69 149.157 670.69 146.621V110.726C670.69 109.923 670.897 109.131 671.299 108.431L705.369 49.3213C707.136 46.2573 704.921 42.436 701.387 42.436H701.41Z" fill="#1C1C1C"></path></g></svg></a><style data-emotion="css tx38f3">.css-tx38f3{color:var(--chakra-colors-gray-400);font-weight:var(--chakra-fontWeights-bold);font-family:DM Sans;font-style:normal;font-size:16px;line-height:24px;width:-webkit-max-content;width:-moz-max-content;width:max-content;margin-bottom:var(--chakra-space-4);margin-top:0px;}@media screen and (min-width: 62em){.css-tx38f3{margin-top:var(--chakra-space-5);}}</style><p class="chakra-text css-tx38f3">© <!-- -->2025<!-- --> Tally</p></div><style data-emotion="css 1xhj18k">.css-1xhj18k{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;}</style><div class="css-1xhj18k"><style data-emotion="css 1r12ow5">.css-1r12ow5{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;min-width:auto;margin-top:var(--chakra-space-4);margin-bottom:var(--chakra-space-4);}@media screen and (min-width: 48em){.css-1r12ow5{min-width:250px;}}@media screen and (min-width: 62em){.css-1r12ow5{margin-top:var(--chakra-space-2);margin-bottom:var(--chakra-space-2);}}</style><div class="css-1r12ow5"><style data-emotion="css odj8tk">.css-odj8tk{font-weight:var(--chakra-fontWeights-bold);margin-bottom:var(--chakra-space-3);font-family:DM Sans;font-style:normal;font-size:16px;line-height:24px;}</style><p class="chakra-text css-odj8tk">Resources</p><style data-emotion="css 2xph3x">.css-2xph3x{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-2);}</style><div class="chakra-stack css-2xph3x"><a target="_blank" rel="noopener" class="chakra-link css-nuz2iz" href="https://docs.tally.xyz"><style data-emotion="css d8mbpa">.css-d8mbpa{color:var(--chakra-colors-gray-500);font-size:var(--chakra-fontSizes-sm);font-weight:var(--chakra-fontWeights-semibold);line-height:1.313rem;font-family:DM Sans;}</style><p class="chakra-text css-d8mbpa">Developer Documentation</p></a><a target="_blank" rel="noopener" class="chakra-link css-nuz2iz" href="https://wiki.tally.xyz/docs"><p class="chakra-text css-d8mbpa">Governance Wiki</p></a><a target="_blank" rel="noopener" class="chakra-link css-nuz2iz" href="https://tally.mirror.xyz/"><p class="chakra-text css-d8mbpa">Blog</p></a><style data-emotion="css 1dxjk3d">.css-1dxjk3d{transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-fast);transition-timing-function:var(--chakra-transition-easing-ease-out);cursor:pointer;-webkit-text-decoration:none;text-decoration:none;outline:2px solid transparent;outline-offset:2px;color:inherit;font-family:DM Sans;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;padding-left:0px;}.css-1dxjk3d:hover,.css-1dxjk3d[data-hover]{-webkit-text-decoration:underline;text-decoration:underline;}.css-1dxjk3d:focus-visible,.css-1dxjk3d[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}</style><a target="_blank" rel="noopener" class="chakra-link css-1dxjk3d" href="https://docs.tally.xyz/knowledge-base/tally"><p class="chakra-text css-d8mbpa">Knowledge Base</p></a><a target="_blank" rel="noopener" class="chakra-link css-1dxjk3d" href="https://status.tally.xyz"><p class="chakra-text css-d8mbpa">System Status</p></a></div></div><style data-emotion="css 1rh8wl2">.css-1rh8wl2{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;min-width:auto;margin-left:var(--chakra-space-10);margin-top:var(--chakra-space-4);margin-bottom:var(--chakra-space-4);}@media screen and (min-width: 30em){.css-1rh8wl2{margin-left:var(--chakra-space-18);}}@media screen and (min-width: 48em){.css-1rh8wl2{min-width:250px;}}@media screen and (min-width: 62em){.css-1rh8wl2{margin-top:var(--chakra-space-2);margin-bottom:var(--chakra-space-2);}}</style><div class="css-1rh8wl2"><p class="chakra-text css-odj8tk">Company</p><div class="chakra-stack css-2xph3x"><a target="_blank" rel="noopener" class="chakra-link css-nuz2iz" href="https://careers.tally.xyz"><p class="chakra-text css-d8mbpa">Jobs</p></a><a class="chakra-link css-nuz2iz" href="/brandkit"><p class="chakra-text css-d8mbpa">Brand Kit</p></a><a target="_blank" rel="noopener" class="chakra-link css-nuz2iz" href="https://terms.tally.xyz/"><p class="chakra-text css-d8mbpa">Terms of Service</p></a><a target="_blank" rel="noopener" class="chakra-link css-nuz2iz" href="https://www.iubenda.com/privacy-policy/20084196"><p class="chakra-text css-d8mbpa">Privacy Policy</p></a></div></div></div></div><style data-emotion="css tlwbng">.css-tlwbng{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-align-items:flex-end;-webkit-box-align:flex-end;-ms-flex-align:flex-end;align-items:flex-end;margin-top:0px;margin-bottom:0px;padding-top:var(--chakra-space-1);width:var(--chakra-sizes-full);}@media screen and (min-width: 30em){.css-tlwbng{padding-top:var(--chakra-space-2);}}@media screen and (min-width: 62em){.css-tlwbng{margin-top:var(--chakra-space-2);margin-bottom:var(--chakra-space-2);}}</style><div class="css-tlwbng"><style data-emotion="css atsqzg">.css-atsqzg{display:none;font-weight:var(--chakra-fontWeights-bold);margin-bottom:var(--chakra-space-3);text-align:right;font-family:DM Sans;font-style:normal;font-size:16px;line-height:24px;width:var(--chakra-sizes-full);}@media screen and (min-width: 30em){.css-atsqzg{display:block;width:auto;}}</style><p class="chakra-text css-atsqzg">Join our Community!</p><style data-emotion="css 16qay40">.css-16qay40{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;gap:0.5rem;width:var(--chakra-sizes-full);}@media screen and (min-width: 62em){.css-16qay40{-webkit-box-pack:end;-ms-flex-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;}}</style><address class="chakra-stack css-16qay40"><a target="_blank" rel="noopener" class="chakra-link no-underline css-nuz2iz" href="https://twitter.com/tallyxyz"><style data-emotion="css 1v2e9b2">.css-1v2e9b2{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;white-space:nowrap;vertical-align:middle;outline:2px solid transparent;outline-offset:2px;line-height:1.2;border-radius:var(--chakra-radii-md);font-weight:var(--chakra-fontWeights-semibold);transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);font-family:DM Sans;height:var(--chakra-sizes-10);min-width:var(--chakra-sizes-10);font-size:var(--chakra-fontSizes-sm);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);padding:0px;background:var(--chakra-colors-white);color:var(--chakra-colors-gray-400);}.css-1v2e9b2:focus-visible,.css-1v2e9b2[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-1v2e9b2:disabled,.css-1v2e9b2[disabled],.css-1v2e9b2[aria-disabled=true],.css-1v2e9b2[data-disabled]{opacity:0.4;cursor:not-allowed;box-shadow:var(--chakra-shadows-none);}.css-1v2e9b2:hover,.css-1v2e9b2[data-hover]{background:var(--chakra-colors-gray-200);}.css-1v2e9b2:hover:disabled,.css-1v2e9b2[data-hover]:disabled,.css-1v2e9b2:hover[disabled],.css-1v2e9b2[data-hover][disabled],.css-1v2e9b2:hover[aria-disabled=true],.css-1v2e9b2[data-hover][aria-disabled=true],.css-1v2e9b2:hover[data-disabled],.css-1v2e9b2[data-hover][data-disabled]{background:var(--chakra-colors-gray-100);}.css-1v2e9b2:active,.css-1v2e9b2[data-active]{background:var(--chakra-colors-gray-300);}</style><button type="button" class="chakra-button css-1v2e9b2" aria-label="Go to Twitter profile"><style data-emotion="css 13agqcd">.css-13agqcd{display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:var(--chakra-colors-gray-800);height:var(--chakra-sizes-5);width:var(--chakra-sizes-5);}</style><style data-emotion="css k1uwu9">.css-k1uwu9{width:1em;height:1em;display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;vertical-align:middle;display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:var(--chakra-colors-gray-800);height:var(--chakra-sizes-5);width:var(--chakra-sizes-5);}</style><svg viewBox="0 0 21 18" focusable="false" class="chakra-icon chakra-icon css-k1uwu9" aria-hidden="true"><path d="M18.8444 4.60934C19.6655 3.99351 20.4045 3.25452 20.9793 2.39236C20.2403 2.7208 19.3781 2.96713 18.516 3.04924C19.4192 2.51552 20.0761 1.69442 20.4045 0.668033C19.5834 1.1607 18.6391 1.53019 17.6948 1.73547C16.8737 0.873309 15.7652 0.380645 14.5336 0.380645C12.1524 0.380645 10.2228 2.31025 10.2228 4.69145C10.2228 5.0199 10.2638 5.34834 10.3459 5.67678C6.77413 5.47151 3.57181 3.74718 1.43694 1.1607C1.06744 1.77653 0.862162 2.51552 0.862162 3.33663C0.862162 4.81462 1.60116 6.12839 2.79176 6.90844C2.09382 6.86739 1.39588 6.70316 0.821106 6.37472V6.41578C0.821106 8.5096 2.2991 10.2339 4.26975 10.6445C3.94131 10.7266 3.53076 10.8087 3.16126 10.8087C2.87387 10.8087 2.62754 10.7676 2.34015 10.7266C2.87387 12.4509 4.47503 13.6826 6.36357 13.7236C4.88558 14.8732 3.03809 15.5711 1.02638 15.5711C0.656885 15.5711 0.328443 15.5301 0 15.489C1.88854 16.7207 4.14659 17.4186 6.60991 17.4186C14.5336 17.4186 18.8444 10.8908 18.8444 5.18412C18.8444 4.97884 18.8444 4.81462 18.8444 4.60934Z" fill="currentColor"></path></svg></button></a><a target="_blank" rel="noopener" class="chakra-link no-underline css-nuz2iz" href="https://github.com/withtally/"><style data-emotion="css dzhtog">.css-dzhtog{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;white-space:nowrap;vertical-align:middle;outline:2px solid transparent;outline-offset:2px;line-height:1.2;border-radius:var(--chakra-radii-md);font-weight:var(--chakra-fontWeights-semibold);transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);font-family:DM Sans;height:var(--chakra-sizes-10);min-width:var(--chakra-sizes-10);font-size:var(--chakra-fontSizes-sm);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);color:var(--chakra-colors-gray-800);padding:0px;background:var(--chakra-colors-white);}.css-dzhtog:focus-visible,.css-dzhtog[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-dzhtog:disabled,.css-dzhtog[disabled],.css-dzhtog[aria-disabled=true],.css-dzhtog[data-disabled]{opacity:0.4;cursor:not-allowed;box-shadow:var(--chakra-shadows-none);}.css-dzhtog:hover,.css-dzhtog[data-hover]{background:var(--chakra-colors-gray-200);}.css-dzhtog:hover:disabled,.css-dzhtog[data-hover]:disabled,.css-dzhtog:hover[disabled],.css-dzhtog[data-hover][disabled],.css-dzhtog:hover[aria-disabled=true],.css-dzhtog[data-hover][aria-disabled=true],.css-dzhtog:hover[data-disabled],.css-dzhtog[data-hover][data-disabled]{background:var(--chakra-colors-gray-100);}.css-dzhtog:active,.css-dzhtog[data-active]{background:var(--chakra-colors-gray-300);}</style><button type="button" class="chakra-button css-dzhtog" aria-label="Go to GitHub profile"><style data-emotion="css 18rw4e7">.css-18rw4e7{display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;height:var(--chakra-sizes-5);width:var(--chakra-sizes-5);}</style><style data-emotion="css 1m0gzrn">.css-1m0gzrn{width:1em;height:1em;display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;vertical-align:middle;display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;height:var(--chakra-sizes-5);width:var(--chakra-sizes-5);}</style><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-icon css-1m0gzrn" aria-hidden="true"><path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22" fill="currentColor"></path></svg></button></a></address><style data-emotion="css ronz7c">.css-ronz7c{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;margin-top:var(--chakra-space-4);}@media screen and (min-width: 62em){.css-ronz7c{margin-top:var(--chakra-space-8);}}</style><div class="css-ronz7c"><style data-emotion="css 116ty0a">.css-116ty0a{height:38px;}</style><img alt="Alchemy Supercharged" src="https://static.alchemyapi.io/images/marketing/rollups-badge-dark-1.png" class="chakra-image css-116ty0a" id="badge-button"/></div></div></footer></div><span></span><span id="__chakra_env" hidden=""></span></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"chainId":"eip155:42161","proposal":{"id":"2662020087342433698","onchainId":"51852039695020109312343918128899814224888993575448130385109956762385891284115","metadata":{"title":"[CONSTITUTIONAL] Remove Cost Cap, Update Executors, Disable Legacy USDT Bridge","description":"# [CONSTITUTIONAL] Remove Cost Cap, Update Executors, Disable Legacy USDT Bridge\n# **Abstract**\n\nThis AIP combines and puts forward three separate proposals for the ArbitrumDAO to consider: [\u003cu\u003eRemove Cost Cap on Arbitrum Nova\u003c/u\u003e](https://snapshot.box/#/s:arbitrumfoundation.eth/proposal/0xeb044e3bdeba71e74dea02abeab4c33fe4bf5ab9def50082863702d5e2112b93), [\u003cu\u003eUpdate the Upgrade Executors\u003c/u\u003e](https://snapshot.box/#/s:arbitrumfoundation.eth/proposal/0xc08147e229fa642ee7da2e5180f225122b8c517024902f21cdcd02b376179b8d), and [\u003cu\u003eDisable Legacy Tether Bridge\u003c/u\u003e](https://snapshot.box/#/s:arbitrumfoundation.eth/proposal/0xd5a66f784523841511f7ffec4171b9c14404fdbf7c205086312084d19c95c193). All of the aforementioned proposals have previously passed a Snapshot vote, reaching a quorum of 3% votable $ARB. \n\nThe first proposal will remove the Amortized Cost Cap, which is expected to result in higher gas fees for users on Arbitrum Nova. This increase is necessary to ensure the long-term sustainability of the network and reduce the financial burden on the Arbitrum Foundation.\n\nThe second proposal will replace the Upgrade Executor contracts on Arbitrum One, Arbitrum Nova, and Ethereum mainnet with upgraded versions. The modernized version introduces an additional function that enables the Upgrade Executor to execute an upgrade by directly calling the target contract, rather than indirectly delegate calling an upgrade contract.\n\nThe third proposal seeks to disable the legacy USDT bridge for Arbitrum One, given the activation of the new [\u003cu\u003eUSDT0 bridge\u003c/u\u003e](https://mirror.xyz/tetherzero.eth/_6FNgGi0WHHQhA9qavZ4rlt-nV9ehVuJUHxQnSwOmbM) in January 2025. This proposal seeks to disable the legacy bridge entirely, rather than continue with the current configuration, which subjects depositors to a week-long delay and risks loss of funds if the depositor is a smart contract.\n\n# **Motivation \u0026 Rationale**\n\n## Remove Cost Cap on Nova\n\nArbitrum Nova was initially positioned as a cost-efficient alternative to Arbitrum One, offering 30-50x cheaper transaction fees at launch. However, several developments have significantly reduced Nova’s competitive advantage:\n\n* EIP-4844 blobs: The introduction of EIP-4844 significantly reduced the cost difference between Nova and Arbitrum One, from 30-50x to a mere 2x. This difference is negligible given the already low costs on Arbitrum One.\n* Orbit adoption: Orbit chains have become a more attractive option for teams seeking gas sponsorship and customization. Their ease of deployment has led many teams to prefer Orbit chains over Nova.\n\nGiven the now-minimal cost advantage over Arbitrum One and the rise of Orbit chains, maintaining an amortized cost cap on Nova — which currently prevents batch posters from recovering full L1 costs — is no longer justifiable. Removing the cap will stop the Arbitrum Foundation from providing subsidies and align cost recovery with actual usage.\n\nAccordingly, removing the cap (i.e., setting it to 0 bips) would:\n\n* Ensure batch posters are able to recover 100% of their L1 posting costs.\n* Eliminate recurring deficits currently paid by the Arbitrum Foundation.\n* Reflect Nova’s lower strategic priority within the broader Arbitrum roadmap.\n\nHistorical data on Arbitrum Nova batch poster deficits can be seen [\u003cu\u003ehere\u003c/u\u003e](https://app.hex.tech/8bb08a30-5c58-48c4-acb0-5a0515db9db2/app/Public-Arbitrum-Nova-Economics-02zr64hC21SVqt2HSQjRLk/latest). ​This data highlights the ongoing inefficiencies and justifies the removal of the amortized cost cap.\n\n## Update the Upgrade Executors\n\nThe Upgrade Executors controlled by the DAO are responsible for carrying out critical upgrades on [\u003cu\u003eArbitrum One\u003c/u\u003e](https://arbiscan.io/address/******************************************#code), [\u003cu\u003eArbitrum Nova\u003c/u\u003e](https://nova.arbiscan.io/address/******************************************), and [\u003cu\u003eEthereum\u003c/u\u003e](https://etherscan.io/address/******************************************#code) such as protocol contract upgrades, system parameter changes, and more. Currently, the traditional Upgrade Executor contracts in place only support one function for executing an upgrade: [\u003cu\u003eexecute()\u003c/u\u003e](https://arbiscan.io/address/******************************************#writeProxyContract#F1). When called by the owner of the Upgrade Executor, the execute() function will perform a delegate call to an upgrade contract, which facilitates subsequent calls to target contracts and ultimately executes the upgrade. This indirect method of calling and execution can complicate upgrades and require additional development and deployments.\n\nThe modern version of the Upgrade Executor introduces a new function, [\u003cu\u003eexecuteCall()\u003c/u\u003e](https://github.com/OffchainLabs/upgrade-executor/blob/eaa9d607464abb8683a3fe526acbf6d877f924e0/src/UpgradeExecutor.sol#L73), which allows the Upgrade Executor to directly call target contracts and execute upgrades, removing the need for delegate calls to upgrade contracts. While the existing execute() call is still supported, the new executeCall() function enables the Upgrade Executor to implement upgrades in a lighter weight fashion. In the future, upgrades that use this method may not require dedicated action contracts, simplifying the process and saving development energy.\n\n## Disable Legacy Tether Bridge\n\nHistorically, the existing USDT bridge for Arbitrum One was the primary path for bridging Tether to and from Arbitrum One. The new [\u003cu\u003eUSDT0 standard\u003c/u\u003e](https://mirror.xyz/tetherzero.eth/_6FNgGi0WHHQhA9qavZ4rlt-nV9ehVuJUHxQnSwOmbM) introduced a modernized alternative that has become the go-to solution. However, the legacy USDT bridge is still technically enabled, which presents several issues.\n\nFirst, withdrawals from the legacy bridge to L1 are subject to a seven-day delay, which is meaningfully slower than the new USDT0 bridge which can process bridge transactions in seconds or minutes. Also, once this delay has elapsed, deposits are auto-withdrawn on L1. This inconvenience can be addressed by disabling the legacy bridge and directing users exclusively to the new USDT0 bridge.\n\nSecond, user funds could be lost if a smart contract deposits to the legacy bridge on their behalf. The legacy bridge is currently configured to auto-withdraw funds as soon as possible back to the depositor; however, this auto-withdraw configuration can cause problems for smart contracts that deposit on behalf of end users but do not have withdrawal or refund functionality. While it’s possible to recover funds in this scenario, it is certainly sub-optimal for smart contract operators and end users that delegate to them. Disabling the legacy bridge prevents smart contracts from encountering this issue and putting funds at risk. Also, once the legacy bridge is disabled, any transaction submitted to it will simply revert; this applies to third party integrations as well as direct user interactions.\n\n# **Key Terms**\n\n## Remove Cost Cap on Nova\n\n* **Amortized Cost Cap (setAmortizedCostCapBips)**: A parameter within the ArbOwner system contract that limits the percentage of Layer 1 transaction posting costs that batch posters are allowed to recover from the network. Expressed in basis points (bips), where 10000 = 100%.\n* **ArbOwner Contract**: A privileged precompiled contract that allows the ArbitrumDAO to configure core protocol settings, such as the amortized cost cap.\n* **EIP-4844**: An Ethereum Improvement Proposal introducing blob-carrying transactions. Its implementation has significantly reduced the cost disparity between Arbitrum Nova and Arbitrum One, undermining the original economic rationale for Nova’s fee model.\n* **Batch poster transactions**: Transactions that aggregate multiple operations and submit them as a single on-chain transaction. With the enforced fee cap, these transactions become less cost-effective under current network dynamics, contributing to operational inefficiencies.\n\n## Update the Upgrade Executors\n\n* **Upgrade Executor**: A smart contract responsible for executing upgrades on Arbitrum One, Arbitrum Nova, and Ethereum mainnet; owned and administered by the DAO\n* **Upgrade Contract**: A smart contract that contains dedicated logic for a particular upgrade; receives a call from the Upgrade Executor and subsequently calls other contracts targeted in that upgrade\n* **execute()**: The sole function for executing an upgrade in existing Upgrade Executor implementations; relies on indirect delegate calls to upgrade contracts\n* **executeCall()**: A [\u003cu\u003enew function\u003c/u\u003e](https://github.com/OffchainLabs/upgrade-executor/blob/eaa9d607464abb8683a3fe526acbf6d877f924e0/src/UpgradeExecutor.sol#L73) for executing an upgrade in the modern Upgrade Executor implementation; allows for direct calls to target contracts. A new [\u003cu\u003eaudit report\u003c/u\u003e](https://docs.arbitrum.io/assets/files/2025-07-offchainlabs-upgrade-executor-report-900fbe3a31c6ca81e2949355f6134d33.pdf) was recently completed. Trail of Bits reviewed the implementation of executeCall, assessed it against the proposal’s intent, and checked for unintended side effects. Trail of Bits found no security-relevant issues. \n\n## Disable Legacy Tether Bridge\n\n* **USDT0**: Tether’s new OFT-based version of USDT; more details here\n* DisableGatewayAction: A smart contract action that disables a gateway for a specified token from the [\u003cu\u003eL1GatewayRouter\u003c/u\u003e](https://etherscan.io/address/******************************************#code); the DisableGatewayAction source code can be found [\u003cu\u003ehere\u003c/u\u003e](https://github.com/ArbitrumFoundation/governance/blob/main/src/gov-action-contracts/token-bridge/DisableGatewayAction.sol)\n\n# **Specifications \u0026 Steps to Implement**\n\n## Remove Cost Cap on Nova\n\nThe DAO will execute a call to the [\u003cu\u003eArbOwner contract\u003c/u\u003e](https://github.com/OffchainLabs/nitro-contracts/blob/c32af127fe6a9124316abebbf756609649ede1f5/src/precompiles/ArbOwner.sol#L129C14-L129C37) on Arbitrum Nova to set the amortizedCostCapBips value to 0, thereby allowing batch posters to recover 100% of their L1 posting costs.\n\n## Update the Upgrade Executors\n\n* Deploy the latest [\u003cu\u003eUpgradeExecutor\u003c/u\u003e](https://github.com/OffchainLabs/upgrade-executor/blob/main/src/UpgradeExecutor.sol) contract on each network\n* Update proxy contracts on each network to reflect new UpgradeExecutor addresses:\n  * Arbitrum One: [\u003cu\u003e0xCF57572261c7c2BCF21ffD220ea7d1a27D40A827\u003c/u\u003e](https://arbiscan.io/address/******************************************#code)\n    * [\u003cu\u003eupgradeTo\u003c/u\u003e](https://arbiscan.io/address/******************************************#writeContract#F4): ******************************************\n  * Arbitrum Nova: [\u003cu\u003e******************************************\u003c/u\u003e](https://nova.arbiscan.io/address/******************************************)\n    * [\u003cu\u003eupgradeTo\u003c/u\u003e](https://nova.arbiscan.io/address/******************************************#writeContract#F4): ******************************************\n  * Ethereum: [\u003cu\u003e******************************************\u003c/u\u003e](https://etherscan.io/address/******************************************#code)\n    * [\u003cu\u003eupgradeTo\u003c/u\u003e](https://etherscan.io/address/******************************************#writeContract#F4): ******************************************\n\n1. Deploy new UpgradeExecutor contracts on Arbitrum One, Arbitrum Nova, and Ethereum using the parameters specified above\n2. Update proxy contracts on Arbitrum One, Arbitrum Nova, and Ethereum to point to the new UpgradeExecutor contracts. The action described in this proposal is contained inside the payload uploaded to Tally. \n\n## Disable Legacy Tether Bridge\n\nDeactivate Legacy Bridge: execute the ‘[\u003cu\u003eperform\u003c/u\u003e](https://etherscan.io/address/******************************************#writeContract#F1)’ function on the DisableGatewayAction contract, disabling legacy USDT on the L1GatewayRouter contract\n\n* DisableGatewayAction: [\u003cu\u003e******************************************\u003c/u\u003e](https://etherscan.io/address/******************************************#code)\n* L1GatewayRouter: [\u003cu\u003e******************************************\u003c/u\u003e](https://etherscan.io/address/******************************************#code)\n* Legacy USDT: [\u003cu\u003e******************************************\u003c/u\u003e](https://etherscan.io/address/******************************************)\n\n \n\n1. Contract Execution: call DisableGatewayAction contract to disable legacy USDT\n2. The audit report for the DisableGatewayAction contract can be found [\u003cu\u003ehere\u003c/u\u003e](https://docs.arbitrum.io/assets/files/2025-03-offchain-disablegateway-action-securityreview-11ed2e1370d062c2ade5e5d6b085a8f3.pdf). The action described in this proposal is contained inside the payload uploaded to Tally.","snapshotURL":"","discourseURL":""},"status":"active","events":[{"block":null,"type":"drafted"},{"block":{"number":369846189},"type":"created"},{"block":null,"type":"activated"}],"governor":{"id":"eip155:42161:******************************************","chainId":"eip155:42161","organization":{"name":"Arbitrum","metadata":{"icon":"https://static.tally.xyz/4053a112-4234-4788-ae89-3c3939a913df_original.jpeg"}}}},"organization":{"id":"2206072050315953936","name":"Arbitrum","slug":"arbitrum","chainIds":["eip155:42161"],"governorIds":["eip155:42161:0x47F5Cc1ac2d088Ea7Cd9bBDBE7c96f81A9196CfF","eip155:42161:0x1A90be02bBF03BA21922C19De6e5962166fcE73d","eip155:42161:******************************************","eip155:42161:******************************************","eip155:42161:******************************************","eip155:42161:******************************************","eip155:42161:******************************************","eip155:42161:******************************************"],"tokenIds":["eip155:42161/erc20:******************************************"],"metadata":{"description":"Secure scaling for Ethereum","icon":"https://static.tally.xyz/4053a112-4234-4788-ae89-3c3939a913df_original.jpeg","socials":{"website":"","discord":"","telegram":"","twitter":"https://twitter.com/arbitrum","discourse":null,"others":[{"label":"Snapshot","value":"https://snapshot.org/#/arbitrumfoundation.eth"},{"label":"Forum","value":"https://forum.arbitrum.foundation/"}]},"karmaName":"arbitrum"},"features":[{"name":"EXCLUDEFEE","enabled":true},{"name":"DELEGATIONWEEK","enabled":false},{"name":"LEGACY_LAYOUT","enabled":false},{"name":"COUNCIL","enabled":true},{"name":"EXCLUDE_TALLY_FEE","enabled":true},{"name":"DISPLAY_COMPLIANCE_NOTICE","enabled":true},{"name":"DISPLAY_FEATURED_DELEGATES","enabled":true},{"name":"PREDELEGATIONWEEK","enabled":false},{"name":"SHOW_ARB_STAKING","enabled":false},{"name":"SECURITY_COUNCIL_BANNER","enabled":true},{"name":"DELEGATES_SORTED_BY_PRIORITY","enabled":false}],"contracts":[]},"governors":[{"id":"eip155:42161:******************************************","name":"Arbitrum Core","kind":"multiprimary","quorum":"208621757459928726659352610","isPrimary":true,"chainId":"eip155:42161","type":"openzeppelingovernor","contracts":{"governor":{"address":"******************************************","type":"openzeppelingovernor"},"tokens":[{"address":"******************************************","type":"ERC20"}]},"token":{"id":"eip155:42161/erc20:******************************************","decimals":18,"name":"Arbitrum","symbol":"ARB","type":"ERC20"}},{"id":"eip155:42161:******************************************","name":"PL Arb Grants Safety","kind":"multiother","quorum":"46360390546650828146522802","isPrimary":false,"chainId":"eip155:42161","type":"openzeppelingovernor","contracts":{"governor":{"address":"******************************************","type":"openzeppelingovernor"},"tokens":[{"address":"******************************************","type":"ERC20"}]},"token":{"id":"eip155:42161/erc20:******************************************","decimals":18,"name":"Arbitrum","symbol":"ARB","type":"ERC20"}},{"id":"eip155:42161:******************************************","name":"Arbitrum Treasury","kind":"multisecondary","quorum":"139081171639952484439568407","isPrimary":false,"chainId":"eip155:42161","type":"openzeppelingovernor","contracts":{"governor":{"address":"******************************************","type":"openzeppelingovernor"},"tokens":[{"address":"******************************************","type":"ERC20"}]},"token":{"id":"eip155:42161/erc20:******************************************","decimals":18,"name":"Arbitrum","symbol":"ARB","type":"ERC20"}},{"id":"eip155:42161:******************************************","name":"Questbook ARB DAO GRANTS","kind":"multiother","quorum":"46360390546650828146522802","isPrimary":false,"chainId":"eip155:42161","type":"openzeppelingovernor","contracts":{"governor":{"address":"******************************************","type":"openzeppelingovernor"},"tokens":[{"address":"******************************************","type":"ERC20"}]},"token":{"id":"eip155:42161/erc20:******************************************","decimals":18,"name":"Arbitrum","symbol":"ARB","type":"ERC20"}},{"id":"eip155:42161:0x47F5Cc1ac2d088Ea7Cd9bBDBE7c96f81A9196CfF","name":"ArbitrumDAO ARDC [Clawback Capability]","kind":"multiother","quorum":"46360390546650828146522802","isPrimary":false,"chainId":"eip155:42161","type":"openzeppelingovernor","contracts":{"governor":{"address":"0x47F5Cc1ac2d088Ea7Cd9bBDBE7c96f81A9196CfF","type":"openzeppelingovernor"},"tokens":[{"address":"******************************************","type":"ERC20"}]},"token":{"id":"eip155:42161/erc20:******************************************","decimals":18,"name":"Arbitrum","symbol":"ARB","type":"ERC20"}},{"id":"eip155:42161:******************************************","name":"Arbitrum Delegate Incentives Zodiac Module ","kind":"multiother","quorum":"46360390546650828146522802","isPrimary":false,"chainId":"eip155:42161","type":"openzeppelingovernor","contracts":{"governor":{"address":"******************************************","type":"openzeppelingovernor"},"tokens":[{"address":"******************************************","type":"ERC20"}]},"token":{"id":"eip155:42161/erc20:******************************************","decimals":18,"name":"Arbitrum","symbol":"ARB","type":"ERC20"}},{"id":"eip155:42161:0x1A90be02bBF03BA21922C19De6e5962166fcE73d","name":"ArbitrumDAO Procurement - Clawback Capability","kind":"multiother","quorum":"46360390546650828146522802","isPrimary":false,"chainId":"eip155:42161","type":"openzeppelingovernor","contracts":{"governor":{"address":"0x1A90be02bBF03BA21922C19De6e5962166fcE73d","type":"openzeppelingovernor"},"tokens":[{"address":"******************************************","type":"ERC20"}]},"token":{"id":"eip155:42161/erc20:******************************************","decimals":18,"name":"Arbitrum","symbol":"ARB","type":"ERC20"}}],"descriptionExceptions":[],"isWhiteLabel":false,"_sentryTraceData":"abedf2a469b54e5f8b84aa5d0a3b7113-8f8d6a9d674559ee-0","_sentryBaggage":"sentry-environment=preview,sentry-release=589075d2bc1d847df1dad27a1f2f15d92fcaf1ac,sentry-public_key=7346b0b400624973989f24de372411ff,sentry-trace_id=abedf2a469b54e5f8b84aa5d0a3b7113,sentry-sample_rate=0.1,sentry-transaction=%2Fgov%2F%5BgovernanceId%5D%2Fproposal%2F%5BproposalId%5D,sentry-sampled=false"},"__N_SSP":true},"page":"/gov/[governanceId]/proposal/[proposalId]","query":{"govId":"eip155:42161:******************************************","governanceId":"arbitrum","proposalId":"51852039695020109312343918128899814224888993575448130385109956762385891284115"},"buildId":"ea2vspBnjsMfoIY-HtAfV","isFallback":false,"gssp":true,"scriptLoader":[]}</script></body></html>